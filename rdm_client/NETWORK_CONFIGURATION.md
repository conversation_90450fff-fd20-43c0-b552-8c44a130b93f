# TaskFlow 网络配置说明

## 概述

TaskFlow iOS 客户端支持多环境配置，可以根据不同的构建模式自动连接到不同的后端服务器。

## 环境配置

### Debug 模式
- **后端地址**: `http://10.0.136.252:3000/api/v1`
- **Parse Server**: `http://10.0.136.252:3000/parse`
- **用途**: 本地开发和调试
- **特点**: 
  - 允许HTTP连接
  - 启用详细日志
  - 自动测试服务器连接

### Development 模式
- **后端地址**: `https://rdm.sanva.top/api/v1`
- **Parse Server**: `https://rdm.sanva.top/parse`
- **用途**: 开发环境测试
- **特点**:
  - 使用HTTPS连接
  - 启用日志记录
  - 连接到开发服务器

### Production 模式
- **后端地址**: `https://api.taskflow.app/api/v1`
- **Parse Server**: `https://api.taskflow.app/parse`
- **用途**: 生产环境
- **特点**:
  - 使用HTTPS连接
  - 禁用详细日志
  - 最高安全级别

## 配置文件

### 1. Configuration.plist
位置: `rdm/Configuration.plist`

包含所有环境的服务器配置信息，可以在不修改代码的情况下调整服务器地址。

### 2. NetworkConfiguration.swift
位置: `rdm/Core/Network/NetworkConfiguration.swift`

定义网络配置的结构和默认值。

### 3. ConfigurationManager.swift
位置: `rdm/Core/Utils/ConfigurationManager.swift`

负责读取和管理配置信息。

## 如何修改服务器地址

### 方法1: 修改 Configuration.plist
1. 打开 `rdm/Configuration.plist`
2. 找到对应环境的配置
3. 修改 `BaseURL` 和 `ParseServerURL`
4. 重新构建应用

### 方法2: 修改代码中的默认值
1. 打开 `NetworkConfiguration.swift`
2. 修改对应环境的 `ServerConfig`
3. 重新构建应用

## 构建配置

### Xcode 构建设置
- **Debug**: 自动使用Debug配置
- **Release**: 自动使用Production配置
- **自定义**: 可以添加DEVELOPMENT编译标志

### 添加Development构建配置
1. 在Xcode中选择项目
2. 进入Build Settings
3. 添加自定义编译标志: `DEVELOPMENT=1`
4. 在Swift代码中使用 `#if DEVELOPMENT`

## 测试和验证

### 自动测试
应用在Debug模式下启动时会自动：
1. 打印当前配置信息
2. 测试服务器连接
3. 验证URL可达性

### 手动测试
可以调用 `NetworkConfigurationTest.testCurrentConfiguration()` 来手动测试配置。

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否运行
   - 验证IP地址和端口
   - 确认防火墙设置

2. **HTTPS证书错误**
   - 确认域名证书有效
   - 检查证书链完整性

3. **配置不生效**
   - 清理并重新构建项目
   - 检查Configuration.plist是否正确添加到Bundle

### 调试步骤

1. 查看控制台输出的配置信息
2. 确认当前环境检测正确
3. 验证URL构建是否正确
4. 测试网络连接

## 安全注意事项

1. **生产环境**: 确保使用HTTPS
2. **API密钥**: 不要在代码中硬编码敏感信息
3. **证书验证**: 生产环境启用证书固定
4. **日志**: 生产环境禁用敏感信息日志

## 更新记录

- 2024/01/01: 初始版本，支持Debug和Production环境
- 2024/01/01: 添加Development环境支持
- 2024/01/01: 添加配置文件管理和自动测试功能
