<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>ServerConfiguration</key>
    <dict>
        <key>Debug</key>
        <dict>
            <key>BaseURL</key>
            <string>http://10.0.136.252:3000/api/v1</string>
            <key>ParseServerURL</key>
            <string>http://10.0.136.252:3000/parse</string>
            <key>AllowInsecureHTTP</key>
            <true/>
            <key>EnableLogging</key>
            <true/>
        </dict>
        <key>Development</key>
        <dict>
            <key>BaseURL</key>
            <string>https://rdm.sanva.top/api/v1</string>
            <key>ParseServerURL</key>
            <string>https://rdm.sanva.top/parse</string>
            <key>AllowInsecureHTTP</key>
            <false/>
            <key>EnableLogging</key>
            <true/>
        </dict>
        <key>Production</key>
        <dict>
            <key>BaseURL</key>
            <string>https://api.taskflow.app/api/v1</string>
            <key>ParseServerURL</key>
            <string>https://api.taskflow.app/parse</string>
            <key>AllowInsecureHTTP</key>
            <false/>
            <key>EnableLogging</key>
            <false/>
        </dict>
    </dict>
    <key>APIConfiguration</key>
    <dict>
        <key>Version</key>
        <string>v1</string>
        <key>Timeout</key>
        <integer>30</integer>
        <key>MaxRetries</key>
        <integer>3</integer>
        <key>UserAgent</key>
        <string>TaskFlow-iOS/1.0</string>
    </dict>
</dict>
</plist>
