//
//  NetworkConfiguration.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 网络配置管理
struct NetworkConfiguration {
    
    // MARK: - Environment Configuration
    
    /// 当前环境类型
    enum Environment {
        case debug      // Debug模式 - 连接本地IP
        case development // 开发模式 - 连接域名
        case production  // 生产模式 - 连接生产域名
        
        static var current: Environment {
            #if DEBUG
            return .debug
            #elseif DEVELOPMENT
            return .development
            #else
            return .production
            #endif
        }
    }
    
    // MARK: - Server Configuration
    
    /// 服务器配置
    struct ServerConfig {
        let baseURL: String
        let parseServerURL: String
        let timeout: TimeInterval
        let maxRetries: Int
        
        static let debug = ServerConfig(
            baseURL: "http://************:3000/api/v1",
            parseServerURL: "http://************:3000/parse",
            timeout: 30,
            maxRetries: 3
        )

        static let development = ServerConfig(
            baseURL: "https://rdm.sanva.top/api/v1",
            parseServerURL: "https://rdm.sanva.top/parse",
            timeout: 30,
            maxRetries: 3
        )

        static let production = ServerConfig(
            baseURL: "https://api.taskflow.app/api/v1",
            parseServerURL: "https://api.taskflow.app/parse",
            timeout: 30,
            maxRetries: 3
        )

        /// 获取当前环境的配置
        static var current: ServerConfig {
            let configManager = ConfigurationManager.shared
            return ServerConfig(
                baseURL: configManager.baseURL,
                parseServerURL: configManager.parseServerURL,
                timeout: configManager.timeout,
                maxRetries: configManager.maxRetries
            )
        }
    }
    
    // MARK: - API Configuration
    
    /// API相关配置
    struct APIConfig {
        static let version = "v1"
        static let contentType = "application/json"
        static let userAgent = "TaskFlow-iOS/1.0"
        
        /// 请求头配置
        static var defaultHeaders: [String: String] {
            return [
                "Content-Type": contentType,
                "Accept": contentType,
                "User-Agent": userAgent,
                "X-API-Version": version
            ]
        }
    }
    
    // MARK: - Security Configuration
    
    /// 安全配置
    struct SecurityConfig {
        static let allowInsecureHTTP = Environment.current == .debug
        static let certificatePinning = Environment.current == .production
        static let requestEncryption = Environment.current != .debug
    }
    
    // MARK: - Logging Configuration
    
    /// 日志配置
    struct LoggingConfig {
        static let enableNetworkLogging = Environment.current != .production
        static let enableRequestLogging = Environment.current == .debug
        static let enableResponseLogging = Environment.current == .debug
        static let logLevel: LogLevel = {
            switch Environment.current {
            case .debug:
                return .verbose
            case .development:
                return .info
            case .production:
                return .error
            }
        }()
        
        enum LogLevel {
            case verbose
            case info
            case warning
            case error
        }
    }
    
    // MARK: - Cache Configuration
    
    /// 缓存配置
    struct CacheConfig {
        static let enableCaching = true
        static let cacheSize: Int = 50 * 1024 * 1024 // 50MB
        static let cacheTimeout: TimeInterval = 300 // 5分钟
    }
    
    // MARK: - Utility Methods
    
    /// 获取完整的API URL
    static func apiURL(for endpoint: String) -> URL? {
        let baseURL = ServerConfig.current.baseURL
        return URL(string: "\(baseURL)/\(endpoint)")
    }
    
    /// 获取Parse Server URL
    static func parseServerURL() -> URL? {
        return URL(string: ServerConfig.current.parseServerURL)
    }
    
    /// 打印当前配置信息
    static func printCurrentConfiguration() {
        print("=== TaskFlow Network Configuration ===")
        print("Environment: \(Environment.current)")
        print("Base URL: \(ServerConfig.current.baseURL)")
        print("Parse Server URL: \(ServerConfig.current.parseServerURL)")
        print("Timeout: \(ServerConfig.current.timeout)s")
        print("Max Retries: \(ServerConfig.current.maxRetries)")
        print("Allow Insecure HTTP: \(SecurityConfig.allowInsecureHTTP)")
        print("Network Logging: \(LoggingConfig.enableNetworkLogging)")
        print("=====================================")
    }
}
