//
//  NetworkConfigurationTest.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 网络配置测试工具
struct NetworkConfigurationTest {
    
    /// 测试当前网络配置
    static func testCurrentConfiguration() {
        print("\n🔧 Testing Network Configuration...")
        
        // 测试配置管理器
        let configManager = ConfigurationManager.shared
        print("\n📋 Configuration Manager:")
        configManager.printCurrentConfiguration()
        
        // 测试网络配置
        print("\n🌐 Network Configuration:")
        NetworkConfiguration.printCurrentConfiguration()
        
        // 测试API客户端
        print("\n📡 API Client Configuration:")
        let apiClient = APIClient.shared
        print("API Client initialized successfully")
        
        // 测试URL构建
        print("\n🔗 URL Testing:")
        if let healthURL = NetworkConfiguration.apiURL(for: "health") {
            print("Health endpoint URL: \(healthURL)")
        }
        
        if let parseURL = NetworkConfiguration.parseServerURL() {
            print("Parse Server URL: \(parseURL)")
        }
        
        // 测试环境检测
        print("\n🏷️ Environment Detection:")
        print("Current Environment: \(NetworkConfiguration.Environment.current)")
        
        #if DEBUG
        print("✅ Running in DEBUG mode - will connect to: \(configManager.baseURL)")
        #elseif DEVELOPMENT
        print("✅ Running in DEVELOPMENT mode - will connect to: \(configManager.baseURL)")
        #else
        print("✅ Running in PRODUCTION mode - will connect to: \(configManager.baseURL)")
        #endif
        
        print("\n✅ Network configuration test completed!\n")
    }
    
    /// 验证URL可达性
    static func validateURLReachability() async {
        print("🔍 Testing URL reachability...")
        
        let configManager = ConfigurationManager.shared
        let baseURL = configManager.baseURL
        
        guard let url = URL(string: baseURL) else {
            print("❌ Invalid base URL: \(baseURL)")
            return
        }
        
        do {
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                print("✅ Server responded with status: \(httpResponse.statusCode)")
            }
        } catch {
            print("❌ Failed to reach server: \(error.localizedDescription)")
            print("💡 Make sure the server is running at: \(baseURL)")
        }
    }
}
