/**
 * TaskFlow Server - Main Entry Point
 * Express.js + Parse Server implementation
 */

require('dotenv').config();
const express = require('express');
const { ParseServer } = require('parse-server');
const ParseDashboard = require('parse-dashboard');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const logger = require('./src/utils/logger');
const { connectDatabase } = require('./src/config/database');
const { setupMiddleware } = require('./src/middleware');
const routes = require('./src/routes');

// 应用配置
const app = express();
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || 'localhost';

// Parse Server 配置
const parseConfig = {
  databaseURI: process.env.MONGODB_URI || 'mongodb://localhost:27017/taskflow',
  cloud: './src/cloud/main.js',
  appId: process.env.PARSE_APP_ID || 'taskflow-app-id',
  masterKey: process.env.PARSE_MASTER_KEY || 'taskflow-master-key',
  serverURL: process.env.PARSE_SERVER_URL || `http://localhost:${PORT}/parse`,
  mountPath: process.env.PARSE_MOUNT_PATH || '/parse',
  
  // 用户认证配置
  enableAnonymousUsers: false,
  allowClientClassCreation: false,
  
  // 文件上传配置
  maxUploadSize: '10mb',
  
  // 推送通知配置 (开发环境暂时禁用)
  // push: {
  //   ios: {
  //     pfx: process.env.APNS_PRIVATE_KEY_PATH,
  //     bundleId: process.env.APNS_BUNDLE_ID || 'com.taskflow.app',
  //     production: process.env.APNS_PRODUCTION === 'true'
  //   }
  // },
  
  // 邮件配置 (开发环境暂时禁用)
  // emailAdapter: {
  //   module: '@parse/simple-mailgun-adapter',
  //   options: {
  //     fromAddress: process.env.FROM_EMAIL || '<EMAIL>',
  //     domain: process.env.MAILGUN_DOMAIN,
  //     apiKey: process.env.MAILGUN_API_KEY,
  //   }
  // },
  
  // 安全配置
  sessionLength: 604800, // 7 days
  revokeSessionOnPasswordReset: true,

  // 新版本配置选项 (解决deprecation warnings)
  encodeParseObjectInCloudFunction: true,
  allowExpiredAuthDataToken: false,
  enableInsecureAuthAdapters: false,

  // 日志配置
  logLevel: process.env.LOG_LEVEL || 'info',
  
  // 自定义页面
  customPages: {
    invalidLink: process.env.INVALID_LINK_URL,
    verifyEmailSuccess: process.env.VERIFY_EMAIL_SUCCESS_URL,
    choosePassword: process.env.CHOOSE_PASSWORD_URL,
    passwordResetSuccess: process.env.PASSWORD_RESET_SUCCESS_URL
  }
};

// Parse Dashboard 配置
const dashboardConfig = {
  apps: [
    {
      serverURL: parseConfig.serverURL,
      appId: parseConfig.appId,
      masterKey: parseConfig.masterKey,
      appName: 'TaskFlow'
    }
  ],
  users: [
    {
      user: process.env.PARSE_DASHBOARD_USER || 'admin',
      pass: process.env.PARSE_DASHBOARD_PASS || 'admin'
    }
  ],
  useEncryptedPasswords: false,
  allowInsecureHTTP: process.env.PARSE_DASHBOARD_ALLOW_INSECURE_HTTP === 'true',
  // 禁用已弃用的 PublicAPIRouter
  pages: {
    enableRouter: false
  }
};

// 速率限制配置
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 100个请求
  message: {
    error: 'Too many requests from this IP, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 初始化服务器
 */
async function initializeServer() {
  try {
    // 连接数据库
    await connectDatabase();
    logger.info('Database connected successfully');

    // 安全中间件
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS 配置
    const corsOptions = {
      origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : '*',
      credentials: true,
      optionsSuccessStatus: 200
    };
    app.use(cors(corsOptions));

    // 基础中间件
    app.use(compression());
    app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 速率限制
    app.use('/api', limiter);

    // 自定义中间件
    setupMiddleware(app);

    // Parse Server
    const parseServer = new ParseServer(parseConfig);
    app.use(parseConfig.mountPath, parseServer.app);

    // Parse Dashboard (仅开发环境)
    if (process.env.ENABLE_PARSE_DASHBOARD === 'true') {
      const dashboard = new ParseDashboard(dashboardConfig);
      app.use('/dashboard', dashboard);
      logger.info('Parse Dashboard enabled at /dashboard');
    }

    // API 路由
    app.use('/api/v1', routes);

    // 健康检查端点
    app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
      });
    });

    // 根路径
    app.get('/', (req, res) => {
      res.json({
        message: 'TaskFlow API Server',
        version: '1.0.0',
        documentation: '/api/v1/docs',
        dashboard: process.env.ENABLE_PARSE_DASHBOARD === 'true' ? '/dashboard' : null
      });
    });

    // 404 处理
    app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Endpoint not found'
        }
      });
    });

    // 全局错误处理
    app.use((err, req, res, next) => {
      logger.error('Unhandled error:', err);
      
      res.status(err.status || 500).json({
        success: false,
        error: {
          code: err.code || 'INTERNAL_ERROR',
          message: process.env.NODE_ENV === 'production' 
            ? 'Internal server error' 
            : err.message
        }
      });
    });

    // 启动服务器
    app.listen(PORT, HOST, () => {
      logger.info(`TaskFlow Server running on http://${HOST}:${PORT}`);
      logger.info(`Parse Server running on http://${HOST}:${PORT}${parseConfig.mountPath}`);
      if (process.env.ENABLE_PARSE_DASHBOARD === 'true') {
        logger.info(`Parse Dashboard running on http://${HOST}:${PORT}/dashboard`);
      }
    });

  } catch (error) {
    logger.error('Failed to initialize server:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// 启动服务器
initializeServer();
