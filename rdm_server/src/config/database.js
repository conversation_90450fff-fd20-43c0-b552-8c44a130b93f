/**
 * 数据库连接配置
 * MongoDB 连接和配置管理
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');

/**
 * MongoDB 连接配置
 */
const mongooseOptions = {
  maxPoolSize: 10, // 连接池最大连接数
  serverSelectionTimeoutMS: 5000, // 服务器选择超时
  socketTimeoutMS: 45000, // Socket 超时
  family: 4, // 使用 IPv4
  retryWrites: true,
  w: 'majority'
};

/**
 * 连接到 MongoDB 数据库
 */
async function connectDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/taskflow';
    
    logger.info('Connecting to MongoDB...');
    
    await mongoose.connect(mongoUri, mongooseOptions);
    
    logger.info('MongoDB connected successfully');
    
    // 监听连接事件
    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB connection error:', error);
    });
    
    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });
    
    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
    });
    
    // 优雅关闭
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (error) {
        logger.error('Error closing MongoDB connection:', error);
        process.exit(1);
      }
    });
    
  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

/**
 * 获取数据库连接状态
 */
function getDatabaseStatus() {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  
  return {
    status: states[mongoose.connection.readyState],
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    name: mongoose.connection.name
  };
}

/**
 * 数据库健康检查
 */
async function checkDatabaseHealth() {
  try {
    await mongoose.connection.db.admin().ping();
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      ...getDatabaseStatus()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
      ...getDatabaseStatus()
    };
  }
}

/**
 * 创建数据库索引
 */
async function createIndexes() {
  try {
    logger.info('Creating database indexes...');
    
    // 用户相关索引
    await mongoose.connection.db.collection('_User').createIndexes([
      { key: { email: 1 }, unique: true },
      { key: { username: 1 }, unique: true },
      { key: { createdAt: 1 } }
    ]);
    
    // 任务相关索引
    await mongoose.connection.db.collection('Task').createIndexes([
      { key: { userId: 1, createdAt: -1 } },
      { key: { userId: 1, isCompleted: 1 } },
      { key: { userId: 1, priority: 1 } },
      { key: { userId: 1, dueDate: 1 } },
      { key: { updatedAt: 1 } }
    ]);
    
    // 习惯相关索引
    await mongoose.connection.db.collection('Habit').createIndexes([
      { key: { userId: 1, createdAt: -1 } },
      { key: { userId: 1, isActive: 1 } },
      { key: { updatedAt: 1 } }
    ]);
    
    // 习惯记录索引
    await mongoose.connection.db.collection('HabitRecord').createIndexes([
      { key: { habitId: 1, date: -1 } },
      { key: { userId: 1, date: -1 } },
      { key: { createdAt: 1 } }
    ]);
    
    // 会话索引
    await mongoose.connection.db.collection('_Session').createIndexes([
      { key: { user: 1 } },
      { key: { expiresAt: 1 }, expireAfterSeconds: 0 }
    ]);
    
    logger.info('Database indexes created successfully');
    
  } catch (error) {
    logger.error('Failed to create database indexes:', error);
    throw error;
  }
}

/**
 * 数据库清理任务
 */
async function cleanupDatabase() {
  try {
    logger.info('Running database cleanup...');
    
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // 清理过期的会话
    const expiredSessions = await mongoose.connection.db
      .collection('_Session')
      .deleteMany({ expiresAt: { $lt: now } });
    
    logger.info(`Cleaned up ${expiredSessions.deletedCount} expired sessions`);
    
    // 清理软删除的任务（30天前）
    const deletedTasks = await mongoose.connection.db
      .collection('Task')
      .deleteMany({ 
        isDeleted: true, 
        deletedAt: { $lt: thirtyDaysAgo } 
      });
    
    logger.info(`Cleaned up ${deletedTasks.deletedCount} soft-deleted tasks`);
    
    // 清理软删除的习惯（30天前）
    const deletedHabits = await mongoose.connection.db
      .collection('Habit')
      .deleteMany({ 
        isDeleted: true, 
        deletedAt: { $lt: thirtyDaysAgo } 
      });
    
    logger.info(`Cleaned up ${deletedHabits.deletedCount} soft-deleted habits`);
    
    logger.info('Database cleanup completed');
    
  } catch (error) {
    logger.error('Database cleanup failed:', error);
    throw error;
  }
}

/**
 * 数据库备份信息
 */
function getBackupInfo() {
  return {
    recommendation: 'Setup automated backups for production',
    commands: {
      backup: 'mongodump --uri="$MONGODB_URI" --out=./backup',
      restore: 'mongorestore --uri="$MONGODB_URI" ./backup'
    },
    schedule: 'Daily at 2:00 AM UTC',
    retention: '30 days'
  };
}

module.exports = {
  connectDatabase,
  getDatabaseStatus,
  checkDatabaseHealth,
  createIndexes,
  cleanupDatabase,
  getBackupInfo
};
