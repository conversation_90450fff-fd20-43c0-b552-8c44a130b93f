/**
 * 中间件配置
 * 统一管理所有自定义中间件
 */

const { requestLogger, logError, logSecurityEvent } = require('../utils/logger');
const rateLimit = require('express-rate-limit');

/**
 * 请求ID中间件 - 为每个请求生成唯一ID
 */
function requestId(req, res, next) {
  req.id = require('crypto').randomUUID();
  res.setHeader('X-Request-ID', req.id);
  next();
}

/**
 * 用户认证中间件
 */
function requireAuth(req, res, next) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    logSecurityEvent('MISSING_AUTH_TOKEN', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path
    });
    
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTH_REQUIRED',
        message: 'Authentication token required'
      }
    });
  }
  
  // 这里会被 Parse Server 的认证机制处理
  // 我们只是添加额外的日志记录
  next();
}

/**
 * 管理员权限中间件
 */
function requireAdmin(req, res, next) {
  // 检查用户是否为管理员
  if (!req.user || !req.user.get('isAdmin')) {
    logSecurityEvent('ADMIN_ACCESS_DENIED', {
      userId: req.user?.id,
      ip: req.ip,
      endpoint: req.path
    });
    
    return res.status(403).json({
      success: false,
      error: {
        code: 'PERMISSION_DENIED',
        message: 'Admin access required'
      }
    });
  }
  
  next();
}

/**
 * 输入验证中间件
 */
function validateInput(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Input validation failed',
          details: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        }
      });
    }
    
    req.validatedBody = value;
    next();
  };
}

/**
 * 错误处理中间件
 */
function errorHandler(err, req, res, next) {
  logError(err, {
    requestId: req.id,
    method: req.method,
    url: req.url,
    userId: req.user?.id,
    ip: req.ip
  });
  
  // Parse Server 错误处理
  if (err.code && err.message) {
    return res.status(err.code).json({
      success: false,
      error: {
        code: err.error || 'PARSE_ERROR',
        message: err.message
      }
    });
  }
  
  // 默认错误处理
  const statusCode = err.statusCode || err.status || 500;
  const message = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : err.message;
  
  res.status(statusCode).json({
    success: false,
    error: {
      code: err.code || 'INTERNAL_ERROR',
      message
    }
  });
}

/**
 * 404 处理中间件
 */
function notFoundHandler(req, res) {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'Endpoint not found'
    }
  });
}

/**
 * API 速率限制
 */
const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logSecurityEvent('RATE_LIMIT_EXCEEDED', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    res.status(429).json({
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests, please try again later'
      }
    });
  }
});

/**
 * 严格的速率限制（用于敏感操作）
 */
const strictRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP最多5个请求
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many attempts, please try again later'
    }
  }
});

/**
 * 响应时间中间件
 */
function responseTime(req, res, next) {
  const start = Date.now();

  // 在响应开始前设置头部
  const originalSend = res.send;
  res.send = function(data) {
    const duration = Date.now() - start;

    // 只在响应还未发送时设置头部
    if (!res.headersSent) {
      res.setHeader('X-Response-Time', `${duration}ms`);
    }

    // 记录慢请求
    if (duration > 1000) {
      logError(new Error('Slow request detected'), {
        duration: `${duration}ms`,
        method: req.method,
        url: req.url,
        userId: req.user?.id
      });
    }

    return originalSend.call(this, data);
  };

  next();
}

/**
 * 安全头中间件
 */
function securityHeaders(req, res, next) {
  // 防止点击劫持
  res.setHeader('X-Frame-Options', 'DENY');
  
  // 防止 MIME 类型嗅探
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // XSS 保护
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // 引用者策略
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
}

/**
 * CORS 预检请求处理
 */
function handleCors(req, res, next) {
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Parse-Application-Id, X-Parse-REST-API-Key');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24小时
    return res.status(200).end();
  }
  next();
}

/**
 * 设置所有中间件
 */
function setupMiddleware(app) {
  // 基础中间件
  app.use(requestId);
  app.use(responseTime);
  app.use(securityHeaders);
  app.use(handleCors);
  app.use(requestLogger);
  
  // 全局错误处理
  app.use(errorHandler);
}

module.exports = {
  setupMiddleware,
  requestId,
  requireAuth,
  requireAdmin,
  validateInput,
  errorHandler,
  notFoundHandler,
  apiRateLimit,
  strictRateLimit,
  responseTime,
  securityHeaders,
  handleCors
};
