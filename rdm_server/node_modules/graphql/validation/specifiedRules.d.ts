import { MaxIntrospectionDepthRule } from './rules/MaxIntrospectionDepthRule';
import type { SDLValidationRule, ValidationRule } from './ValidationContext';
/**
 * Technically these aren't part of the spec but they are strongly encouraged
 * validation rules.
 */
export declare const recommendedRules: readonly typeof MaxIntrospectionDepthRule[];
/**
 * This set includes all validation rules defined by the GraphQL spec.
 *
 * The order of the rules in this list has been adjusted to lead to the
 * most clear output when encountering multiple validation errors.
 */
export declare const specifiedRules: ReadonlyArray<ValidationRule>;
/**
 * @internal
 */
export declare const specifiedSDLRules: ReadonlyArray<SDLValidationRule>;
