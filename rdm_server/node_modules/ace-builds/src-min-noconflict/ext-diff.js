ace.define("ace/ext/diff/scroll_diff_decorator",["require","exports","module","ace/layer/decorators"],function(e,t,n){var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n),t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=this&&this.__values||function(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=e("../../layer/decorators").Decorator,o=function(e){function t(t,n,r){var i=e.call(this,t,n)||this;return i.colors.dark["delete"]="rgba(255, 18, 18, 1)",i.colors.dark.insert="rgba(18, 136, 18, 1)",i.colors.light["delete"]="rgb(255,51,51)",i.colors.light.insert="rgb(32,133,72)",i.$zones=[],i.$forInlineDiff=r,i}return r(t,e),t.prototype.addZone=function(e,t,n){this.$zones.push({startRow:e,endRow:t,type:n})},t.prototype.setSessions=function(e,t){this.sessionA=e,this.sessionB=t},t.prototype.$updateDecorators=function(t){if(typeof this.canvas.getContext!="function")return;e.prototype.$updateDecorators.call(this,t);if(this.$zones.length>0){var n=this.renderer.theme.isDark===!0?this.colors.dark:this.colors.light,r=this.canvas.getContext("2d");this.$setDiffDecorators(r,n)}},t.prototype.$transformPosition=function(e,t){return t=="delete"?this.sessionA.documentToScreenRow(e,0):this.sessionB.documentToScreenRow(e,0)},t.prototype.$setDiffDecorators=function(e,t){function o(e,t){return e.from===t.from?e.to-t.to:e.from-t.from}var n,r,s=this,u=this.$zones;if(u){var a=[],f=u.filter(function(e){return e.type==="delete"}),l=u.filter(function(e){return e.type==="insert"});[f,l].forEach(function(e){e.forEach(function(e,n){var r=s.$transformPosition(e.startRow,e.type)*s.lineHeight,i=s.$transformPosition(e.endRow,e.type)*s.lineHeight+s.lineHeight,o=Math.round(s.heightRatio*r),u=Math.round(s.heightRatio*i),f=1,l=Math.round((o+u)/2),c=u-l;c<s.halfMinDecorationHeight&&(c=s.halfMinDecorationHeight);var h=a[a.length-1];n>0&&h&&h.type===e.type&&l-c<h.to+f&&(l=a[a.length-1].to+f+c),l-c<0&&(l=c),l+c>s.canvasHeight&&(l=s.canvasHeight-c),a.push({type:e.type,from:l-c,to:l+c,color:t[e.type]||null})})}),a=a.sort(o);try{for(var c=i(a),h=c.next();!h.done;h=c.next()){var p=h.value;e.fillStyle=p.color||null;var d=p.from,v=p.to,m=v-d;this.$forInlineDiff?e.fillRect(this.oneZoneWidth,d,2*this.oneZoneWidth,m):p.type=="delete"?e.fillRect(this.oneZoneWidth,d,this.oneZoneWidth,m):e.fillRect(2*this.oneZoneWidth,d,this.oneZoneWidth,m)}}catch(g){n={error:g}}finally{try{h&&!h.done&&(r=c.return)&&r.call(c)}finally{if(n)throw n.error}}}},t.prototype.setZoneWidth=function(){this.oneZoneWidth=Math.round(this.canvasWidth/3)},t}(s);t.ScrollDiffDecorator=o}),ace.define("ace/ext/diff/styles-css.js",["require","exports","module"],function(e,t,n){t.cssText='\n/*\n * Line Markers\n */\n.ace_diff {\n    position: absolute;\n    z-index: 0;\n}\n.ace_diff.inline {\n    z-index: 20;\n}\n/*\n * Light Colors \n */\n.ace_diff.insert {\n    background-color: #EFFFF1;\n}\n.ace_diff.delete {\n    background-color: #FFF1F1;\n}\n.ace_diff.aligned_diff {\n    background: rgba(206, 194, 191, 0.26);\n    background: repeating-linear-gradient(\n                45deg,\n              rgba(122, 111, 108, 0.26),\n              rgba(122, 111, 108, 0.26) 5px,\n              rgba(0, 0, 0, 0) 5px,\n              rgba(0, 0, 0, 0) 10px \n    );\n}\n\n.ace_diff.insert.inline {\n    background-color:  rgb(74 251 74 / 18%); \n}\n.ace_diff.delete.inline {\n    background-color: rgb(251 74 74 / 15%);\n}\n\n.ace_diff.delete.inline.empty {\n    background-color: rgba(255, 128, 79, 0.7);\n    width: 2px !important;\n}\n\n.ace_diff.insert.inline.empty {\n    background-color: rgba(49, 230, 96, 0.7);\n    width: 2px !important;\n}\n\n.ace_diff-active-line {\n    border-bottom: 1px solid;\n    border-top: 1px solid;\n    background: transparent;\n    position: absolute;\n    box-sizing: border-box;\n    border-color: #9191ac;\n}\n\n.ace_dark .ace_diff-active-line {\n    background: transparent;\n    border-color: #75777a;\n}\n \n\n/* gutter changes */\n.ace_mini-diff_gutter-enabled > .ace_gutter-cell,\n.ace_mini-diff_gutter-enabled > .ace_gutter-cell_svg-icons {\n    padding-right: 13px;\n}\n\n.ace_mini-diff_gutter_other > .ace_gutter-cell,\n.ace_mini-diff_gutter_other > .ace_gutter-cell_svg-icons  {\n    display: none;\n}\n\n.ace_mini-diff_gutter_other {\n    pointer-events: none;\n}\n\n\n.ace_mini-diff_gutter-enabled > .mini-diff-added {\n    background-color: #EFFFF1;\n    border-left: 3px solid #2BB534;\n    padding-left: 16px;\n    display: block;\n}\n\n.ace_mini-diff_gutter-enabled > .mini-diff-deleted {\n    background-color: #FFF1F1;\n    border-left: 3px solid #EA7158;\n    padding-left: 16px;\n    display: block;\n}\n\n\n.ace_mini-diff_gutter-enabled > .mini-diff-added:after {\n    position: absolute;\n    right: 2px;\n    content: "+";\n    color: darkgray;\n    background-color: inherit;\n}\n\n.ace_mini-diff_gutter-enabled > .mini-diff-deleted:after {\n    position: absolute;\n    right: 2px;\n    content: "-";\n    color: darkgray;\n    background-color: inherit;\n}\n.ace_fade-fold-widgets:hover > .ace_folding-enabled > .mini-diff-added:after,\n.ace_fade-fold-widgets:hover > .ace_folding-enabled > .mini-diff-deleted:after {\n    display: none;\n}\n\n.ace_diff_other .ace_selection {\n    filter: drop-shadow(1px 2px 3px darkgray);\n}\n\n.ace_hidden_marker-layer .ace_bracket {\n    display: none;\n}\n\n\n\n/*\n * Dark Colors \n */\n\n.ace_dark .ace_diff.insert {\n    background-color: #212E25;\n}\n.ace_dark .ace_diff.delete {\n    background-color: #3F2222;\n}\n\n.ace_dark .ace_mini-diff_gutter-enabled > .mini-diff-added {\n    background-color: #212E25;\n    border-left-color:#00802F;\n}\n\n.ace_dark .ace_mini-diff_gutter-enabled > .mini-diff-deleted {\n    background-color: #3F2222;\n    border-left-color: #9C3838;\n}\n\n'}),ace.define("ace/ext/diff/gutter_decorator",["require","exports","module","ace/lib/dom"],function(e,t,n){var r=e("../../lib/dom"),i=function(){function e(e,t){this.gutterClass="ace_mini-diff_gutter-enabled",this.gutterCellsClasses={add:"mini-diff-added","delete":"mini-diff-deleted"},this.editor=e,this.type=t,this.chunks=[],this.attachToEditor()}return e.prototype.attachToEditor=function(){this.renderGutters=this.renderGutters.bind(this),r.addCssClass(this.editor.renderer.$gutterLayer.element,this.gutterClass),this.editor.renderer.$gutterLayer.on("afterRender",this.renderGutters)},e.prototype.renderGutters=function(e,t){var n=this,r=this.editor.renderer.$gutterLayer.$lines.cells;r.forEach(function(e){e.element.classList.remove(Object.values(n.gutterCellsClasses))});var i=this.type===-1?"old":"new",s=this.type===-1?this.gutterCellsClasses.delete:this.gutterCellsClasses.add;this.chunks.forEach(function(e){var t=e[i].start.row,n=e[i].end.row-1;r.forEach(function(e){e.row>=t&&e.row<=n&&e.element.classList.add(s)})})},e.prototype.setDecorations=function(e){this.chunks=e,this.renderGutters()},e.prototype.dispose=function(){r.removeCssClass(this.editor.renderer.$gutterLayer.element,this.gutterClass),this.editor.renderer.$gutterLayer.off("afterRender",this.renderGutters)},e}();t.MinimalGutterDiffDecorator=i}),ace.define("ace/ext/diff/base_diff_view",["require","exports","module","ace/lib/oop","ace/range","ace/lib/dom","ace/config","ace/line_widgets","ace/ext/diff/scroll_diff_decorator","ace/ext/diff/styles-css.js","ace/editor","ace/virtual_renderer","ace/undomanager","ace/layer/decorators","ace/theme/textmate","ace/multi_select","ace/edit_session","ace/ext/diff/gutter_decorator"],function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,s=[],o;try{while((t===void 0||t-->0)&&!(i=r.next()).done)s.push(i.value)}catch(u){o={error:u}}finally{try{i&&!i.done&&(n=r["return"])&&n.call(r)}finally{if(o)throw o.error}}return s},i=e("../../lib/oop"),s=e("../../range").Range,o=e("../../lib/dom"),u=e("../../config"),a=e("../../line_widgets").LineWidgets,f=e("./scroll_diff_decorator").ScrollDiffDecorator,l=e("./styles-css.js").cssText,c=e("../../editor").Editor,h=e("../../virtual_renderer").VirtualRenderer,p=e("../../undomanager").UndoManager,d=e("../../layer/decorators").Decorator;e("../../theme/textmate"),e("../../multi_select");var v=e("../../edit_session").EditSession,m=e("./gutter_decorator").MinimalGutterDiffDecorator,g={compute:function(e,t,n){return[]}};o.importCssString(l,"diffview.css");var y=function(){function e(e,t){this.onChangeTheme=this.onChangeTheme.bind(this),this.onInput=this.onInput.bind(this),this.onChangeFold=this.onChangeFold.bind(this),this.realign=this.realign.bind(this),this.onSelect=this.onSelect.bind(this),this.onChangeWrapLimit=this.onChangeWrapLimit.bind(this),this.realignPending=!1,this.diffSession,this.chunks,this.inlineDiffEditor=e||!1,this.currentDiffIndex=0,this.diffProvider=g,t&&(this.container=t),this.$ignoreTrimWhitespace=!1,this.$maxDiffs=5e3,this.$maxComputationTimeMs=150,this.$syncSelections=!1,this.$foldUnchangedOnInput=!1,this.markerB=new E(this,1),this.markerA=new E(this,-1)}return e.prototype.$setupModels=function(e){e.diffProvider&&this.setProvider(e.diffProvider),this.showSideA=e.inline==undefined?!0:e.inline==="a";var t={scrollPastEnd:.5,highlightActiveLine:!1,highlightGutterLine:!1,animatedScroll:!0,customScrollbar:!0,vScrollBarAlwaysVisible:!0,fadeFoldWidgets:!0,showFoldWidgets:!0,selectionStyle:"text"};this.savedOptionsA=e.editorA&&e.editorA.getOptions(t),this.savedOptionsB=e.editorB&&e.editorB.getOptions(t);if(!this.inlineDiffEditor||e.inline==="a")this.editorA=e.editorA||this.$setupModel(e.sessionA,e.valueA),this.container&&this.container.appendChild(this.editorA.container),this.editorA.setOptions(t);if(!this.inlineDiffEditor||e.inline==="b")this.editorB=e.editorB||this.$setupModel(e.sessionB,e.valueB),this.container&&this.container.appendChild(this.editorB.container),this.editorB.setOptions(t);if(this.inlineDiffEditor){this.activeEditor=this.showSideA?this.editorA:this.editorB,this.otherSession=this.showSideA?this.sessionB:this.sessionA;var n=this.activeEditor.getOptions();n.readOnly=!0,delete n.mode,this.otherEditor=new c(new h(null),undefined,n),this.showSideA?this.editorB=this.otherEditor:this.editorA=this.otherEditor}this.setDiffSession({sessionA:e.sessionA||(e.editorA?e.editorA.session:new v(e.valueA||"")),sessionB:e.sessionB||(e.editorB?e.editorB.session:new v(e.valueB||"")),chunks:[]}),this.setupScrollbars()},e.prototype.addGutterDecorators=function(){this.gutterDecoratorA||(this.gutterDecoratorA=new m(this.editorA,-1)),this.gutterDecoratorB||(this.gutterDecoratorB=new m(this.editorB,1))},e.prototype.$setupModel=function(e,t){var n=new c(new h,e);return n.session.setUndoManager(new p),t!=undefined&&n.setValue(t,-1),n},e.prototype.foldUnchanged=function(){var e=this.chunks,t="-".repeat(120),n={old:new s(0,0,0,0),"new":new s(0,0,0,0)},r=!1;for(var i=0;i<e.length+1;i++){var o=e[i]||{old:new s(this.sessionA.getLength(),0,this.sessionA.getLength(),0),"new":new s(this.sessionB.getLength(),0,this.sessionB.getLength(),0)},u=o.new.start.row-n.new.end.row-5;if(u>2){var a=n.old.end.row+2,f=this.sessionA.addFold(t,new s(a,0,a+u,Number.MAX_VALUE));a=n.new.end.row+2;var l=this.sessionB.addFold(t,new s(a,0,a+u,Number.MAX_VALUE));if(f||l)r=!0;l&&f&&(f.other=l,l.other=f)}n=o}return r},e.prototype.unfoldUnchanged=function(){var e=this.sessionA.getAllFolds();for(var t=e.length-1;t>=0;t--){var n=e[t];n.placeholder.length==120&&this.sessionA.removeFold(n)}},e.prototype.toggleFoldUnchanged=function(){this.foldUnchanged()||this.unfoldUnchanged()},e.prototype.setDiffSession=function(e){this.diffSession&&(this.$detachSessionsEventHandlers(),this.clearSelectionMarkers()),this.diffSession=e,this.sessionA=this.sessionB=null,this.diffSession&&(this.chunks=this.diffSession.chunks||[],this.editorA&&this.editorA.setSession(e.sessionA),this.editorB&&this.editorB.setSession(e.sessionB),this.sessionA=this.diffSession.sessionA,this.sessionB=this.diffSession.sessionB,this.$attachSessionsEventHandlers(),this.initSelectionMarkers()),this.otherSession=this.showSideA?this.sessionB:this.sessionA},e.prototype.$attachSessionsEventHandlers=function(){},e.prototype.$detachSessionsEventHandlers=function(){},e.prototype.getDiffSession=function(){return this.diffSession},e.prototype.setTheme=function(e){this.editorA&&this.editorA.setTheme(e),this.editorB&&this.editorB.setTheme(e)},e.prototype.getTheme=function(){return(this.editorA||this.editorB).getTheme()},e.prototype.onChangeTheme=function(e){var t=e&&e.theme||this.getTheme();this.editorA&&this.editorA.getTheme()!==t&&this.editorA.setTheme(t),this.editorB&&this.editorB.getTheme()!==t&&this.editorB.setTheme(t)},e.prototype.resize=function(e){this.editorA&&this.editorA.resize(e),this.editorB&&this.editorB.resize(e)},e.prototype.scheduleOnInput=function(){var e=this;if(this.$onInputTimer)return;this.$onInputTimer=setTimeout(function(){e.$onInputTimer=null,e.onInput()})},e.prototype.onInput=function(){var e=this;this.$onInputTimer&&clearTimeout(this.$onInputTimer);var t=this.sessionA.doc.getAllLines(),n=this.sessionB.doc.getAllLines();this.selectionRangeA=null,this.selectionRangeB=null;var r=this.$diffLines(t,n);this.diffSession.chunks=this.chunks=r,this.gutterDecoratorA&&this.gutterDecoratorA.setDecorations(r),this.gutterDecoratorB&&this.gutterDecoratorB.setDecorations(r);if(this.chunks&&this.chunks.length>this.$maxDiffs)return;this.align(),this.editorA&&this.editorA.renderer.updateBackMarkers(),this.editorB&&this.editorB.renderer.updateBackMarkers(),setTimeout(function(){e.updateScrollBarDecorators()},0),this.$foldUnchangedOnInput&&this.foldUnchanged()},e.prototype.setupScrollbars=function(){var e=this,t=function(t){setTimeout(function(){e.$setScrollBarDecorators(t),e.updateScrollBarDecorators()},0)};this.inlineDiffEditor?t(this.activeEditor.renderer):(t(this.editorA.renderer),t(this.editorB.renderer))},e.prototype.$setScrollBarDecorators=function(e){e.$scrollDecorator&&e.$scrollDecorator.destroy(),e.$scrollDecorator=new f(e.scrollBarV,e,this.inlineDiffEditor),e.$scrollDecorator.setSessions(this.sessionA,this.sessionB),e.scrollBarV.setVisible(!0),e.scrollBarV.element.style.bottom=e.scrollBarH.getHeight()+"px"},e.prototype.$resetDecorators=function(e){e.$scrollDecorator&&e.$scrollDecorator.destroy(),e.$scrollDecorator=new d(e.scrollBarV,e)},e.prototype.updateScrollBarDecorators=function(){var e=this;if(this.inlineDiffEditor){if(!this.activeEditor)return;this.activeEditor.renderer.$scrollDecorator.$zones=[]}else{if(!this.editorA||!this.editorB)return;this.editorA.renderer.$scrollDecorator.$zones=[],this.editorB.renderer.$scrollDecorator.$zones=[]}var t=function(e,t){if(!e)return;if(typeof e.renderer.$scrollDecorator.addZone!="function")return;t.old.start.row!=t.old.end.row&&e.renderer.$scrollDecorator.addZone(t.old.start.row,t.old.end.row-1,"delete"),t.new.start.row!=t.new.end.row&&e.renderer.$scrollDecorator.addZone(t.new.start.row,t.new.end.row-1,"insert")};this.inlineDiffEditor?(this.chunks&&this.chunks.forEach(function(n){t(e.activeEditor,n)}),this.activeEditor.renderer.$scrollDecorator.$updateDecorators(this.activeEditor.renderer.layerConfig)):(this.chunks&&this.chunks.forEach(function(n){t(e.editorA,n),t(e.editorB,n)}),this.editorA.renderer.$scrollDecorator.$updateDecorators(this.editorA.renderer.layerConfig),this.editorB.renderer.$scrollDecorator.$updateDecorators(this.editorB.renderer.layerConfig))},e.prototype.$diffLines=function(e,t){return this.diffProvider.compute(e,t,{ignoreTrimWhitespace:this.$ignoreTrimWhitespace,maxComputationTimeMs:this.$maxComputationTimeMs})},e.prototype.setProvider=function(e){this.diffProvider=e},e.prototype.$addWidget=function(e,t){var n=e.lineWidgets[t.row];n&&(t.rowsAbove+=n.rowsAbove>t.rowsAbove?n.rowsAbove:t.rowsAbove,t.rowCount+=n.rowCount),e.lineWidgets[t.row]=t,e.widgetManager.lineWidgets[t.row]=t,e.$resetRowCache(t.row);var r=e.getFoldAt(t.row,0);r&&e.widgetManager.updateOnFold({data:r,action:"add"},e)},e.prototype.$initWidgets=function(e){var t=e.session;t.widgetManager||(t.widgetManager=new a(t),t.widgetManager.attach(e)),e.session.lineWidgets=[],e.session.widgetManager.lineWidgets=[],e.session.$resetRowCache(0)},e.prototype.$screenRow=function(e,t){var n=t.documentToScreenPosition(e).row,r=e.row-t.getLength()+1;return r>0&&(n+=r),n},e.prototype.align=function(){},e.prototype.onChangeWrapLimit=function(e,t){},e.prototype.onSelect=function(e,t){this.searchHighlight(t),this.syncSelect(t)},e.prototype.syncSelect=function(e){if(this.$updatingSelection)return;var t=e.session===this.sessionA,n=e.getRange(),r=t?this.selectionRangeA:this.selectionRangeB;if(r&&n.isEqual(r))return;t?this.selectionRangeA=n:this.selectionRangeB=n,this.$updatingSelection=!0;var i=this.transformRange(n,t);this.$syncSelections&&(t?this.editorB:this.editorA).session.selection.setSelectionRange(i),this.$updatingSelection=!1,t?(this.selectionRangeA=n,this.selectionRangeB=i):(this.selectionRangeA=i,this.selectionRangeB=n),this.updateSelectionMarker(this.syncSelectionMarkerA,this.sessionA,this.selectionRangeA),this.updateSelectionMarker(this.syncSelectionMarkerB,this.sessionB,this.selectionRangeB)},e.prototype.updateSelectionMarker=function(e,t,n){e.setRange(n),t._signal("changeFrontMarker")},e.prototype.onChangeFold=function(e,t){var n=e.data;if(this.$syncingFold||!n||!e.action)return;this.scheduleRealign();var r=t===this.sessionA,i=r?this.sessionB:this.sessionA;e.action==="remove"&&(n.other?(n.other.other=null,i.removeFold(n.other)):n.lineWidget&&(i.widgetManager.addLineWidget(n.lineWidget),n.lineWidget=null,i.$editor&&i.$editor.renderer.updateBackMarkers()));if(e.action==="add"){var s=this.transformRange(n.range,r);if(s.isEmpty()){var o=s.start.row+1;i.lineWidgets[o]&&(n.lineWidget=i.lineWidgets[o],i.widgetManager.removeLineWidget(n.lineWidget),i.$editor&&i.$editor.renderer.updateBackMarkers())}else this.$syncingFold=!0,n.other=i.addFold(n.placeholder,s),n.other&&(n.other.other=n),this.$syncingFold=!1}},e.prototype.scheduleRealign=function(){this.realignPending||(this.realignPending=!0,this.editorA.renderer.on("beforeRender",this.realign),this.editorB.renderer.on("beforeRender",this.realign))},e.prototype.realign=function(){this.realignPending=!0,this.editorA.renderer.off("beforeRender",this.realign),this.editorB.renderer.off("beforeRender",this.realign),this.align(),this.realignPending=!1},e.prototype.detach=function(){if(!this.editorA||!this.editorB)return;this.savedOptionsA&&this.editorA.setOptions(this.savedOptionsA),this.savedOptionsB&&this.editorB.setOptions(this.savedOptionsB),this.editorA.renderer.off("beforeRender",this.realign),this.editorB.renderer.off("beforeRender",this.realign),this.$detachEventHandlers(),this.$removeLineWidgets(this.sessionA),this.$removeLineWidgets(this.sessionB),this.gutterDecoratorA&&this.gutterDecoratorA.dispose(),this.gutterDecoratorB&&this.gutterDecoratorB.dispose(),this.sessionA.selection.clearSelection(),this.sessionB.selection.clearSelection(),this.savedOptionsA&&this.savedOptionsA.customScrollbar&&this.$resetDecorators(this.editorA.renderer),this.savedOptionsB&&this.savedOptionsB.customScrollbar&&this.$resetDecorators(this.editorB.renderer)},e.prototype.$removeLineWidgets=function(e){e.lineWidgets=[],e.widgetManager.lineWidgets=[],e._signal("changeFold",{data:{start:{row:0}}})},e.prototype.$detachEventHandlers=function(){},e.prototype.destroy=function(){this.detach(),this.editorA&&this.editorA.destroy(),this.editorB&&this.editorB.destroy(),this.editorA=this.editorB=null},e.prototype.gotoNext=function(e){var t=this.activeEditor||this.editorA;this.inlineDiffEditor&&(t=this.editorA);var n=t==this.editorA,r=t.selection.lead.row,i=this.findChunkIndex(this.chunks,r,n),o=this.chunks[i+e]||this.chunks[i],u=t.session.getScrollTop();if(o){var a=o[n?"old":"new"],f=Math.max(a.start.row,a.end.row-1);t.selection.setRange(new s(f,0,f,0))}t.renderer.scrollSelectionIntoView(t.selection.lead,t.selection.anchor,.5),t.renderer.animateScrolling(u)},e.prototype.firstDiffSelected=function(){return this.currentDiffIndex<=1},e.prototype.lastDiffSelected=function(){return this.currentDiffIndex>this.chunks.length-1},e.prototype.transformRange=function(e,t){return s.fromPoints(this.transformPosition(e.start,t),this.transformPosition(e.end,t))},e.prototype.transformPosition=function(e,t){var n=this.findChunkIndex(this.chunks,e.row,t),i=this.chunks[n],s=this.sessionB.doc.clonePos,o=s(e),u=r(t?["old","new"]:["new","old"],2),a=u[0],f=u[1],l=0,c=!1;if(i)if(i[a].end.row<=e.row)o.row-=i[a].end.row-i[f].end.row;else if(i.charChanges)for(var h=0;h<i.charChanges.length;h++){var p=i.charChanges[h],d=p[a],v=p[f];if(d.end.row<e.row)continue;if(d.start.row>e.row)break;if(d.isMultiLine()&&d.contains(e.row,e.column)){o.row=v.start.row+e.row-d.start.row;var m=v.end.row;v.end.column===0&&m--,o.row>m&&(o.row=m,o.column=(t?this.sessionB:this.sessionA).getLine(m).length,c=!0),o.row=Math.min(o.row,m)}else{o.row=v.start.row;if(d.start.column>e.column)break;c=!0,!d.isEmpty()&&d.contains(e.row,e.column)?(o.column=v.start.column,l=e.column-d.start.column,l=Math.min(l,v.end.column-v.start.column)):(o=s(v.end),l=e.column-d.end.column)}}else i[a].start.row<=e.row&&(o.row+=i[f].start.row-i[a].start.row,o.row>=i[f].end.row&&(o.row=i[f].end.row-1,o.column=(t?this.sessionB:this.sessionA).getLine(o.row).length));if(!c){var g=r(t?[this.sessionA,this.sessionB]:[this.sessionB,this.sessionA],2),y=g[0],b=g[1];l-=this.$getDeltaIndent(y,b,e.row,o.row)}return o.column+=l,o},e.prototype.$getDeltaIndent=function(e,t,n,r){var i=this.$getIndent(e,n),s=this.$getIndent(t,r);return i-s},e.prototype.$getIndent=function(e,t){return e.getLine(t).match(/^\s*/)[0].length},e.prototype.printDiffs=function(){this.chunks.forEach(function(e){console.log(e.toString())})},e.prototype.findChunkIndex=function(e,t,n){for(var r=0;r<e.length;r++){var i=e[r],s=n?i.old:i.new;if(s.end.row<t)continue;if(s.start.row>t)break}return this.currentDiffIndex=r,r-1},e.prototype.searchHighlight=function(e){if(this.$syncSelections||this.inlineDiffEditor)return;var t=e.session,n=t===this.sessionA?this.sessionB:this.sessionA;n.highlight(t.$searchHighlight.regExp),n._signal("changeBackMarker")},e.prototype.initSelectionMarkers=function(){this.syncSelectionMarkerA=new S,this.syncSelectionMarkerB=new S,this.sessionA.addDynamicMarker(this.syncSelectionMarkerA,!0),this.sessionB.addDynamicMarker(this.syncSelectionMarkerB,!0)},e.prototype.clearSelectionMarkers=function(){this.sessionA.removeMarker(this.syncSelectionMarkerA.id),this.sessionB.removeMarker(this.syncSelectionMarkerB.id)},e}();u.defineOptions(y.prototype,"DiffView",{showOtherLineNumbers:{set:function(e){this.gutterLayer&&(this.gutterLayer.$renderer=e?null:b,this.editorA.renderer.updateFull())},initialValue:!0},folding:{set:function(e){this.editorA.setOption("showFoldWidgets",e),this.editorB.setOption("showFoldWidgets",e);if(!e){var t=[],n=[];this.chunks&&this.chunks.forEach(function(e){t.push(e.old.start,e.old.end),n.push(e.new.start,e.new.end)}),this.sessionA.unfold(t),this.sessionB.unfold(n)}}},syncSelections:{set:function(e){}},ignoreTrimWhitespace:{set:function(e){this.scheduleOnInput()}},wrap:{set:function(e){this.sessionA.setOption("wrap",e),this.sessionB.setOption("wrap",e)}},maxDiffs:{value:5e3},theme:{set:function(e){this.setTheme(e)},get:function(){return this.editorA.getTheme()}}});var b={getText:function(t){return""},getWidth:function(){return 0}};t.BaseDiffView=y;var w=function(){function e(t,n,r){this.old=t,this.new=n,this.charChanges=r&&r.map(function(t){return new e(new s(t.originalStartLineNumber,t.originalStartColumn,t.originalEndLineNumber,t.originalEndColumn),new s(t.modifiedStartLineNumber,t.modifiedStartColumn,t.modifiedEndLineNumber,t.modifiedEndColumn))})}return e}(),E=function(){function e(e,t){this.id,this.diffView=e,this.type=t}return e.prototype.update=function(e,t,n,r){var i,o,u,a=this.diffView;this.type===-1?(i="old",o="delete",u="insert"):(i="new",o="insert",u="delete");var f=a.$ignoreTrimWhitespace,l=a.chunks;if(n.lineWidgets&&!a.inlineDiffEditor)for(var c=r.firstRow;c<=r.lastRow;c++){var h=n.lineWidgets[c];if(!h||h.hidden)continue;var p=n.documentToScreenRow(c,0);if(h.rowsAbove>0){var d=new s(p-h.rowsAbove,0,p-1,Number.MAX_VALUE);t.drawFullLineMarker(e,d,"ace_diff aligned_diff",r)}var v=p+h.rowCount-(h.rowsAbove||0),d=new s(p+1,0,v,Number.MAX_VALUE);t.drawFullLineMarker(e,d,"ace_diff aligned_diff",r)}l.forEach(function(a){var l=a[i].start.row,c=a[i].end.row;if(c<r.firstRow||l>r.lastRow)return;var h=new s(l,0,c-1,1<<30);l!==c&&(h=h.toScreenRange(n),t.drawFullLineMarker(e,h,"ace_diff "+o,r));if(a.charChanges)for(var p=0;p<a.charChanges.length;p++){var d=a.charChanges[p][i];d.end.column==0&&d.end.row>d.start.row&&d.end.row==a[i].end.row&&(d.end.row--,d.end.column=Number.MAX_VALUE);if(f)for(var v=d.start.row;v<=d.end.row;v++){var m=void 0,g=void 0,y=n.getLine(v).match(/^\s*/)[0].length,b=n.getLine(v).length;v===d.start.row?m=d.start.column:m=y,v===d.end.row?g=d.end.column:g=b;var w=new s(v,m,v,g),E=w.toScreenRange(n);if(y===m&&b===g)continue;var S="inline "+o;w.isEmpty()&&m!==0&&(S="inline "+u+" empty"),t.drawSingleLineMarker(e,E,"ace_diff "+S,r)}else{var x=new s(d.start.row,d.start.column,d.end.row,d.end.column),E=x.toScreenRange(n),S="inline "+o;x.isEmpty()&&d.start.column!==0&&(S="inline empty "+u),E.isMultiLine()?t.drawTextMarker(e,E,"ace_diff "+S,r):t.drawSingleLineMarker(e,E,"ace_diff "+S,r)}}})},e}(),S=function(){function e(){this.id,this.type="fullLine",this.clazz="ace_diff-active-line"}return e.prototype.update=function(e,t,n,r){},e.prototype.setRange=function(e){var t=e.clone();t.end.column++,this.range=t},e}();t.DiffChunk=w,t.DiffHighlight=E}),ace.define("ace/ext/diff/inline_diff_view",["require","exports","module","ace/ext/diff/base_diff_view","ace/virtual_renderer","ace/config"],function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n),t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=e("./base_diff_view").BaseDiffView,s=e("../../virtual_renderer").VirtualRenderer,o=e("../../config"),u=function(e){function t(t,n){var r=this;return t=t||{},t.inline=t.inline||"a",r=e.call(this,!0,n)||this,r.init(t),r}return r(t,e),t.prototype.init=function(e){this.onSelect=this.onSelect.bind(this),this.onAfterRender=this.onAfterRender.bind(this),this.$setupModels(e),this.onChangeTheme(),o.resetOptions(this),o._signal("diffView",this);var t=this.activeEditor.renderer.$padding;this.addGutterDecorators(),this.otherEditor.renderer.setPadding(t),this.textLayer=this.otherEditor.renderer.$textLayer,this.markerLayer=this.otherEditor.renderer.$markerBack,this.gutterLayer=this.otherEditor.renderer.$gutterLayer,this.cursorLayer=this.otherEditor.renderer.$cursorLayer,this.otherEditor.renderer.$updateCachedSize=function(){};var n=this.activeEditor.renderer.$textLayer.element;n.parentNode.insertBefore(this.textLayer.element,n);var r=this.activeEditor.renderer.$markerBack.element;r.parentNode.insertBefore(this.markerLayer.element,r.nextSibling);var i=this.activeEditor.renderer.$gutterLayer.element;i.parentNode.insertBefore(this.gutterLayer.element,i.nextSibling),i.style.position="absolute",this.gutterLayer.element.style.position="absolute",this.gutterLayer.element.style.width="100%",this.gutterLayer.element.classList.add("ace_mini-diff_gutter_other"),this.gutterLayer.$updateGutterWidth=function(){},this.initMouse(),this.initTextInput(),this.initTextLayer(),this.initRenderer(),this.$attachEventHandlers(),this.selectEditor(this.activeEditor)},t.prototype.initRenderer=function(e){var t=this;e?delete this.activeEditor.renderer.$getLongestLine:this.editorA.renderer.$getLongestLine=this.editorB.renderer.$getLongestLine=function(){var e=s.prototype.$getLongestLine;return Math.max(e.call(t.editorA.renderer),e.call(t.editorB.renderer))}},t.prototype.initTextLayer=function(){function r(e,t){var r=0,i=e.length-1,s=-1;while(r<i){var o=Math.floor((r+i)/2),u=e[o][n].start.row;if(u<t)s=o,r=o+1;else{if(!(u>t)){s=o;break}i=o-1}}e[s+1]&&e[s+1][n].start.row<=t&&s++;var a=e[s]&&e[s][n];return a&&a.end.row>t?!0:!1}var e=this.textLayer.$renderLine,t=this;this.otherEditor.renderer.$textLayer.$renderLine=function(n,i,s){r(t.chunks,i)&&e.call(this,n,i,s)};var n=this.showSideA?"new":"old"},t.prototype.initTextInput=function(e){e?(this.otherEditor.textInput=this.othertextInput,this.otherEditor.container=this.otherEditorContainer):(this.othertextInput=this.otherEditor.textInput,this.otherEditor.textInput=this.activeEditor.textInput,this.otherEditorContainer=this.otherEditor.container,this.otherEditor.container=this.activeEditor.container)},t.prototype.selectEditor=function(e){e==this.activeEditor?(this.otherEditor.selection.clearSelection(),this.activeEditor.textInput.setHost(this.activeEditor),this.activeEditor.setStyle("ace_diff_other",!1),this.cursorLayer.element.remove(),this.activeEditor.renderer.$cursorLayer.element.style.display="block",this.showSideA&&(this.sessionA.removeMarker(this.syncSelectionMarkerA.id),this.sessionA.addDynamicMarker(this.syncSelectionMarkerA,!0)),this.markerLayer.element.classList.add("ace_hidden_marker-layer"),this.activeEditor.renderer.$markerBack.element.classList.remove("ace_hidden_marker-layer"),this.removeBracketHighlight(this.otherEditor)):(this.activeEditor.selection.clearSelection(),this.activeEditor.textInput.setHost(this.otherEditor),this.activeEditor.setStyle("ace_diff_other"),this.activeEditor.renderer.$cursorLayer.element.parentNode.appendChild(this.cursorLayer.element),this.activeEditor.renderer.$cursorLayer.element.style.display="none",this.activeEditor.$isFocused&&this.otherEditor.onFocus(),this.showSideA&&this.sessionA.removeMarker(this.syncSelectionMarkerA.id),this.markerLayer.element.classList.remove("ace_hidden_marker-layer"),this.activeEditor.renderer.$markerBack.element.classList.add("ace_hidden_marker-layer"),this.removeBracketHighlight(this.activeEditor))},t.prototype.removeBracketHighlight=function(e){var t=e.session;t.$bracketHighlight&&(t.$bracketHighlight.markerIds.forEach(function(e){t.removeMarker(e)}),t.$bracketHighlight=null)},t.prototype.initMouse=function(){var e=this;this.otherEditor.renderer.$loop=this.activeEditor.renderer.$loop,this.otherEditor.renderer.scroller={getBoundingClientRect:function(){return e.activeEditor.renderer.scroller.getBoundingClientRect()},style:this.activeEditor.renderer.scroller.style};var t=function(t){if(!t.domEvent)return;var n=t.editor.renderer.pixelToScreenCoordinates(t.clientX,t.clientY),r=e.activeEditor.session,i=e.otherEditor.session,s=r.screenToDocumentPosition(n.row,n.column,n.offsetX),o=i.screenToDocumentPosition(n.row,n.column,n.offsetX),u=r.documentToScreenPosition(s),a=i.documentToScreenPosition(o);t.editor==e.activeEditor&&(a.row==n.row&&u.row!=n.row?(t.type=="mousedown"&&e.selectEditor(e.otherEditor),t.propagationStopped=!0,t.defaultPrevented=!0,e.otherEditor.$mouseHandler.onMouseEvent(t.type,t.domEvent)):t.type=="mousedown"&&e.selectEditor(e.activeEditor))},n=["mousedown","click","mouseup","dblclick","tripleclick","quadclick"];n.forEach(function(n){e.activeEditor.on(n,t,!0),e.activeEditor.on("gutter"+n,t,!0)});var r=function(t){e.activeEditor.onFocus(t)},i=function(t){e.activeEditor.onBlur(t)};this.otherEditor.on("focus",r),this.otherEditor.on("blur",i),this.onMouseDetach=function(){n.forEach(function(n){e.activeEditor.off(n,t,!0),e.activeEditor.off("gutter"+n,t,!0)}),e.otherEditor.off("focus",r),e.otherEditor.off("blur",i)}},t.prototype.align=function(){var e=this;this.$initWidgets(e.editorA),this.$initWidgets(e.editorB),e.chunks.forEach(function(t){var n=e.$screenRow(t.old.end,e.sessionA)-e.$screenRow(t.old.start,e.sessionA),r=e.$screenRow(t.new.end,e.sessionB)-e.$screenRow(t.new.start,e.sessionB);e.$addWidget(e.sessionA,{rowCount:r,rowsAbove:t.old.end.row===0?r:0,row:t.old.end.row===0?0:t.old.end.row-1}),e.$addWidget(e.sessionB,{rowCount:n,rowsAbove:n,row:t.new.start.row})}),e.sessionA._emit("changeFold",{data:{start:{row:0}}}),e.sessionB._emit("changeFold",{data:{start:{row:0}}})},t.prototype.onChangeWrapLimit=function(){this.sessionB.adjustWrapLimit(this.sessionA.$wrapLimit),this.scheduleRealign()},t.prototype.$attachSessionsEventHandlers=function(){this.$attachSessionEventHandlers(this.editorA,this.markerA),this.$attachSessionEventHandlers(this.editorB,this.markerB),this.sessionA.on("changeWrapLimit",this.onChangeWrapLimit),this.sessionA.on("changeWrapMode",this.onChangeWrapLimit)},t.prototype.$attachSessionEventHandlers=function(e,t){e.session.on("changeFold",this.onChangeFold),e.session.addDynamicMarker(t),e.selection.on("changeCursor",this.onSelect),e.selection.on("changeSelection",this.onSelect)},t.prototype.$detachSessionsEventHandlers=function(){this.$detachSessionHandlers(this.editorA,this.markerA),this.$detachSessionHandlers(this.editorB,this.markerB),this.otherSession.bgTokenizer.lines.fill(undefined),this.sessionA.off("changeWrapLimit",this.onChangeWrapLimit),this.sessionA.off("changeWrapMode",this.onChangeWrapLimit)},t.prototype.$detachSessionHandlers=function(e,t){e.session.removeMarker(t.id),e.selection.off("changeCursor",this.onSelect),e.selection.off("changeSelection",this.onSelect),e.session.off("changeFold",this.onChangeFold)},t.prototype.$attachEventHandlers=function(){this.activeEditor.on("input",this.onInput),this.activeEditor.renderer.on("afterRender",this.onAfterRender),this.otherSession.on("change",this.onInput)},t.prototype.$detachEventHandlers=function(){this.$detachSessionsEventHandlers(),this.activeEditor.off("input",this.onInput),this.activeEditor.renderer.off("afterRender",this.onAfterRender),this.otherSession.off("change",this.onInput),this.textLayer.element.textContent="",this.textLayer.element.remove(),this.gutterLayer.element.textContent="",this.gutterLayer.element.remove(),this.markerLayer.element.textContent="",this.markerLayer.element.remove(),this.onMouseDetach(),this.selectEditor(this.activeEditor),this.clearSelectionMarkers(),this.otherEditor.setSession(null),this.otherEditor.renderer.$loop=null,this.initTextInput(!0),this.initRenderer(!0),this.otherEditor.destroy()},t.prototype.onAfterRender=function(e,t){var n=t.layerConfig,r=this.otherSession,i=this.otherEditor.renderer;r.$scrollTop=t.scrollTop,r.$scrollLeft=t.scrollLeft,["characterWidth","lineHeight","scrollTop","scrollLeft","scrollMargin","$padding","$size","layerConfig","$horizScroll","$vScroll"].forEach(function(e){i[e]=t[e]}),i.$computeLayerConfig();var s=i.layerConfig;this.gutterLayer.update(s),s.firstRowScreen=n.firstRowScreen,i.$cursorLayer.config=s,i.$cursorLayer.update(s),(e&i.CHANGE_LINES||e&i.CHANGE_FULL||e&i.CHANGE_SCROLL||e&i.CHANGE_TEXT)&&this.textLayer.update(s),this.markerLayer.setMarkers(this.otherSession.getMarkers()),this.markerLayer.update(s)},t.prototype.detach=function(){e.prototype.detach.call(this),this.otherEditor&&this.otherEditor.destroy()},t}(i);t.InlineDiffView=u}),ace.define("ace/ext/diff/split_diff_view",["require","exports","module","ace/ext/diff/base_diff_view","ace/config"],function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n),t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=e("./base_diff_view").BaseDiffView,s=e("../../config"),o=function(e){function t(t){var n=this;return t=t||{},n=e.call(this)||this,n.init(t),n}return r(t,e),t.prototype.init=function(e){this.onChangeTheme=this.onChangeTheme.bind(this),this.onMouseWheel=this.onMouseWheel.bind(this),this.onScroll=this.onScroll.bind(this),this.$setupModels(e),this.addGutterDecorators(),this.onChangeTheme(),s.resetOptions(this),s._signal("diffView",this),this.$attachEventHandlers()},t.prototype.onChangeWrapLimit=function(){this.scheduleRealign()},t.prototype.align=function(){var e=this;this.$initWidgets(e.editorA),this.$initWidgets(e.editorB),e.chunks.forEach(function(t){var n=e.$screenRow(t.old.start,e.sessionA),r=e.$screenRow(t.new.start,e.sessionB);n<r?e.$addWidget(e.sessionA,{rowCount:r-n,rowsAbove:t.old.start.row===0?r-n:0,row:t.old.start.row===0?0:t.old.start.row-1}):n>r&&e.$addWidget(e.sessionB,{rowCount:n-r,rowsAbove:t.new.start.row===0?n-r:0,row:t.new.start.row===0?0:t.new.start.row-1});var n=e.$screenRow(t.old.end,e.sessionA),r=e.$screenRow(t.new.end,e.sessionB);n<r?e.$addWidget(e.sessionA,{rowCount:r-n,rowsAbove:t.old.end.row===0?r-n:0,row:t.old.end.row===0?0:t.old.end.row-1}):n>r&&e.$addWidget(e.sessionB,{rowCount:n-r,rowsAbove:t.new.end.row===0?n-r:0,row:t.new.end.row===0?0:t.new.end.row-1})}),e.sessionA._emit("changeFold",{data:{start:{row:0}}}),e.sessionB._emit("changeFold",{data:{start:{row:0}}})},t.prototype.onScroll=function(e,t){this.syncScroll(this.sessionA===t?this.editorA.renderer:this.editorB.renderer)},t.prototype.syncScroll=function(e){if(this.$syncScroll==0)return;var t=this.editorA.renderer,n=this.editorB.renderer,r=e==t;if(t.$scrollAnimation&&n.$scrollAnimation)return;var i=Date.now();if(this.scrollSetBy!=e&&i-this.scrollSetAt<500)return;var s=r?t:n;if(this.scrollSetBy!=e){if(r&&this.scrollA==s.session.getScrollTop())return;if(!r&&this.scrollB==s.session.getScrollTop())return}var o=r?n:t,u=s.session.getScrollTop();this.$syncScroll=!1,r?(this.scrollA=s.session.getScrollTop(),this.scrollB=u):(this.scrollA=u,this.scrollB=s.session.getScrollTop()),this.scrollSetBy=e,o.session.setScrollTop(u),this.$syncScroll=!0,this.scrollSetAt=i},t.prototype.onMouseWheel=function(e){if(e.getAccelKey())return;e.getShiftKey()&&e.wheelY&&!e.wheelX&&(e.wheelX=e.wheelY,e.wheelY=0);var t=e.editor,n=t.renderer.isScrollableBy(e.wheelX*e.speed,e.wheelY*e.speed);if(!n){var r=t==this.editorA?this.editorB:this.editorA;return r.renderer.isScrollableBy(e.wheelX*e.speed,e.wheelY*e.speed)&&r.renderer.scrollBy(e.wheelX*e.speed,e.wheelY*e.speed),e.stop()}},t.prototype.$attachSessionsEventHandlers=function(){this.$attachSessionEventHandlers(this.editorA,this.markerA),this.$attachSessionEventHandlers(this.editorB,this.markerB)},t.prototype.$attachSessionEventHandlers=function(e,t){e.session.on("changeScrollTop",this.onScroll),e.session.on("changeFold",this.onChangeFold),e.session.addDynamicMarker(t),e.selection.on("changeCursor",this.onSelect),e.selection.on("changeSelection",this.onSelect),e.session.on("changeWrapLimit",this.onChangeWrapLimit),e.session.on("changeWrapMode",this.onChangeWrapLimit)},t.prototype.$detachSessionsEventHandlers=function(){this.$detachSessionHandlers(this.editorA,this.markerA),this.$detachSessionHandlers(this.editorB,this.markerB)},t.prototype.$detachSessionHandlers=function(e,t){e.session.off("changeScrollTop",this.onScroll),e.session.off("changeFold",this.onChangeFold),e.session.removeMarker(t.id),e.selection.off("changeCursor",this.onSelect),e.selection.off("changeSelection",this.onSelect),e.session.off("changeWrapLimit",this.onChangeWrapLimit),e.session.off("changeWrapMode",this.onChangeWrapLimit)},t.prototype.$attachEventHandlers=function(){this.editorA.renderer.on("themeChange",this.onChangeTheme),this.editorB.renderer.on("themeChange",this.onChangeTheme),this.editorA.on("mousewheel",this.onMouseWheel),this.editorB.on("mousewheel",this.onMouseWheel),this.editorA.on("input",this.onInput),this.editorB.on("input",this.onInput)},t.prototype.$detachEventHandlers=function(){this.$detachSessionsEventHandlers(),this.clearSelectionMarkers(),this.editorA.renderer.off("themeChange",this.onChangeTheme),this.editorB.renderer.off("themeChange",this.onChangeTheme),this.$detachEditorEventHandlers(this.editorA),this.$detachEditorEventHandlers(this.editorB)},t.prototype.$detachEditorEventHandlers=function(e){e.off("mousewheel",this.onMouseWheel),e.off("input",this.onInput)},t}(i);t.SplitDiffView=o}),ace.define("ace/ext/diff/providers/default",["require","exports","module","ace/range","ace/ext/diff/base_diff_view"],function(e,t,n){"use strict";function h(e,t,n){n===void 0&&(n=function(e,t){return e===t});if(e===t)return!0;if(!e||!t)return!1;if(e.length!==t.length)return!1;for(var r=0,i=e.length;r<i;r++)if(!n(e[r],t[r]))return!1;return!0}function p(e,t){var n,r,o,u,a,f,l,c;return i(this,function(i){switch(i.label){case 0:i.trys.push([0,8,9,10]),o=s(e),u=o.next(),i.label=1;case 1:if(!!u.done)return[3,7];a=u.value;if(r===undefined||!t(r,a))return[3,2];return n.push(a),[3,5];case 2:if(!n)return[3,4];return[4,n];case 3:i.sent(),i.label=4;case 4:n=[a],i.label=5;case 5:r=a,i.label=6;case 6:return u=o.next(),[3,1];case 7:return[3,10];case 8:return f=i.sent(),l={error:f},[3,10];case 9:try{u&&!u.done&&(c=o.return)&&c.call(o)}finally{if(l)throw l.error}return[7];case 10:if(!n)return[3,12];return[4,n];case 11:i.sent(),i.label=12;case 12:return[2]}})}function d(e,t){for(var n=0;n<=e.length;n++)t(n===0?undefined:e[n-1],n===e.length?undefined:e[n])}function v(e,t){for(var n=0;n<e.length;n++)t(n===0?undefined:e[n-1],e[n],n+1===e.length?undefined:e[n+1])}function m(e,t){var n,r;try{for(var i=s(t),o=i.next();!o.done;o=i.next()){var u=o.value;e.push(u)}}catch(a){n={error:a}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}function g(e,t){return function(n,r){return t(e(n),e(r))}}function b(e){return function(t,n){return-e(t,n)}}function E(e,t){t===void 0&&(t="unexpected state");if(!e)throw new w("Assertion Failed: ".concat(t))}function S(e){e()}function x(e,t){var n=0;while(n<e.length-1){var r=e[n],i=e[n+1];if(!t(r,i))return!1;n++}return!0}function k(e,t){var n=L(e,t);return n===-1?undefined:e[n]}function L(e,t,n,r){n===void 0&&(n=0),r===void 0&&(r=e.length);var i=n,s=r;while(i<s){var o=Math.floor((i+s)/2);t(e[o])?i=o+1:s=o}return i-1}function A(e,t){var n=O(e,t);return n===e.length?undefined:e[n]}function O(e,t,n,r){n===void 0&&(n=0),r===void 0&&(r=e.length);var i=n,s=r;while(i<s){var o=Math.floor((i+s)/2);t(e[o])?s=o:i=o+1}return i}function q(e,t){if(e.lineNumber<1)return new N(1,1);if(e.lineNumber>t.length)return new N(t.length,t[t.length-1].length+1);var n=t[e.lineNumber-1];return e.column>n.length+1?new N(e.lineNumber,n.length+1):e}function R(e,t){return e>=1&&e<=t.length}function W(e,t,n,r){var i,o;r===void 0&&(r=!1);var u=[];try{for(var a=s(p(e.map(function(e){return X(e,t,n)}),function(e,t){return e.original.overlapOrTouch(t.original)||e.modified.overlapOrTouch(t.modified)})),f=a.next();!f.done;f=a.next()){var l=f.value,c=l[0],h=l[l.length-1];u.push(new U(c.original.join(h.original),c.modified.join(h.modified),l.map(function(e){return e.innerChanges[0]})))}}catch(d){i={error:d}}finally{try{f&&!f.done&&(o=a.return)&&o.call(a)}finally{if(i)throw i.error}}return S(function(){if(!r&&u.length>0){if(u[0].modified.startLineNumber!==u[0].original.startLineNumber)return!1;if(n.length.lineCount-u[u.length-1].modified.endLineNumberExclusive!==t.length.lineCount-u[u.length-1].original.endLineNumberExclusive)return!1}return x(u,function(e,t){return t.original.startLineNumber-e.original.endLineNumberExclusive===t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber})}),u}function X(e,t,n){var r=0,i=0;e.modifiedRange.endColumn===1&&e.originalRange.endColumn===1&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(i=-1),e.modifiedRange.startColumn-1>=n.getLineLength(e.modifiedRange.startLineNumber)&&e.originalRange.startColumn-1>=t.getLineLength(e.originalRange.startLineNumber)&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+i&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+i&&(r=1);var s=new _(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+i),o=new _(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+i);return new U(s,o,[e])}function Y(e){return e===32||e===9}function ut(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}function at(e){return e>=65&&e<=90}function lt(e){return ft[e]}function ct(e){return e===10?8:e===13?7:Y(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:e===44||e===59?5:4}function ht(e,t,n,r,i,s){var o=dt(e,t,n,s),u=o.moves,a=o.excludedChanges;if(!s.isValid())return[];var f=e.filter(function(e){return!a.has(e)}),l=vt(f,r,i,t,n,s);return m(u,l),u=gt(u),u=u.filter(function(e){var n=e.original.toOffsetRange().slice(t).map(function(e){return e.trim()}),r=n.join("\n");return r.length>=15&&pt(n,function(e){return e.length>=2})>=2}),u=yt(e,u),u}function pt(e,t){var n,r,i=0;try{for(var o=s(e),u=o.next();!u.done;u=o.next()){var a=u.value;t(a)&&i++}}catch(f){n={error:f}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return i}function dt(e,t,n,r){var i,o,u,a,f=[],l=e.filter(function(e){return e.modified.isEmpty&&e.original.length>=3}).map(function(e){return new Z(e.original,t,e)}),c=new Set(e.filter(function(e){return e.original.isEmpty&&e.modified.length>=3}).map(function(e){return new Z(e.modified,n,e)})),h=new Set;try{for(var p=s(l),d=p.next();!d.done;d=p.next()){var v=d.value,m=-1,g=void 0;try{for(var y=(u=void 0,s(c)),b=y.next();!b.done;b=y.next()){var w=b.value,E=v.computeSimilarity(w);E>m&&(m=E,g=w)}}catch(S){u={error:S}}finally{try{b&&!b.done&&(a=y.return)&&a.call(y)}finally{if(u)throw u.error}}m>.9&&g&&(c.delete(g),f.push(new I(v.range,g.range)),h.add(v.source),h.add(g.source));if(!r.isValid())return{moves:f,excludedChanges:h}}}catch(x){i={error:x}}finally{try{d&&!d.done&&(o=p.return)&&o.call(p)}finally{if(i)throw i.error}}return{moves:f,excludedChanges:h}}function vt(e,t,n,r,i,o){var u,a,f,l,c,h,p,d,v=[],m=new st;try{for(var w=s(e),E=w.next();!E.done;E=w.next()){var S=E.value;for(var x=S.original.startLineNumber;x<S.original.endLineNumberExclusive-2;x++){var T="".concat(t[x-1],":").concat(t[x+1-1],":").concat(t[x+2-1]);m.add(T,{range:new _(x,x+3)})}}}catch(N){u={error:N}}finally{try{E&&!E.done&&(a=w.return)&&a.call(w)}finally{if(u)throw u.error}}var C=[];e.sort(g(function(e){return e.modified.startLineNumber},y));var L=function(e){var t=[],r=function(e){var r="".concat(n[e-1],":").concat(n[e+1-1],":").concat(n[e+2-1]),i=new _(e,e+3),o=[];m.forEach(r,function(e){var n,r,u=e.range;try{for(var a=(n=void 0,s(t)),f=a.next();!f.done;f=a.next()){var l=f.value;if(l.originalLineRange.endLineNumberExclusive+1===u.endLineNumberExclusive&&l.modifiedLineRange.endLineNumberExclusive+1===i.endLineNumberExclusive){l.originalLineRange=new _(l.originalLineRange.startLineNumber,u.endLineNumberExclusive),l.modifiedLineRange=new _(l.modifiedLineRange.startLineNumber,i.endLineNumberExclusive),o.push(l);return}}}catch(c){n={error:c}}finally{try{f&&!f.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}var h={modifiedLineRange:i,originalLineRange:u};C.push(h),o.push(h)}),t=o};for(var i=e.modified.startLineNumber;i<e.modified.endLineNumberExclusive-2;i++)r(i);if(!o.isValid())return{value:[]}};try{for(var A=s(e),O=A.next();!O.done;O=A.next()){var S=O.value,P=L(S);if(typeof P=="object")return P.value}}catch(H){f={error:H}}finally{try{O&&!O.done&&(l=A.return)&&l.call(A)}finally{if(f)throw f.error}}C.sort(b(g(function(e){return e.modifiedLineRange.length},y)));var B=new D,j=new D;try{for(var F=s(C),q=F.next();!q.done;q=F.next()){var R=q.value,U=R.modifiedLineRange.startLineNumber-R.originalLineRange.startLineNumber,z=B.subtractFrom(R.modifiedLineRange),W=j.subtractFrom(R.originalLineRange).getWithDelta(U),X=z.getIntersection(W);try{for(var V=(p=void 0,s(X.ranges)),$=V.next();!$.done;$=V.next()){var J=$.value;if(J.length<3)continue;var K=J,Q=J.delta(-U);v.push(new I(Q,K)),B.addRange(K),j.addRange(Q)}}catch(G){p={error:G}}finally{try{$&&!$.done&&(d=V.return)&&d.call(V)}finally{if(p)throw p.error}}}}catch(Y){c={error:Y}}finally{try{q&&!q.done&&(h=F.return)&&h.call(F)}finally{if(c)throw c.error}}v.sort(g(function(e){return e.original.startLineNumber},y));var Z=new M(e),et=function(t){var n=v[t],s=Z.findLastMonotonous(function(e){return e.original.startLineNumber<=n.original.startLineNumber}),u=k(e,function(e){return e.modified.startLineNumber<=n.modified.startLineNumber}),a=Math.max(n.original.startLineNumber-s.original.startLineNumber,n.modified.startLineNumber-u.modified.startLineNumber),f=Z.findLastMonotonous(function(e){return e.original.startLineNumber<n.original.endLineNumberExclusive}),l=k(e,function(e){return e.modified.startLineNumber<n.modified.endLineNumberExclusive}),c=Math.max(f.original.endLineNumberExclusive-n.original.endLineNumberExclusive,l.modified.endLineNumberExclusive-n.modified.endLineNumberExclusive),h=void 0;for(h=0;h<a;h++){var p=n.original.startLineNumber-h-1,d=n.modified.startLineNumber-h-1;if(p>r.length||d>i.length)break;if(B.contains(d)||j.contains(p))break;if(!mt(r[p-1],i[d-1],o))break}h>0&&(j.addRange(new _(n.original.startLineNumber-h,n.original.startLineNumber)),B.addRange(new _(n.modified.startLineNumber-h,n.modified.startLineNumber)));var m=void 0;for(m=0;m<c;m++){var p=n.original.endLineNumberExclusive+m,d=n.modified.endLineNumberExclusive+m;if(p>r.length||d>i.length)break;if(B.contains(d)||j.contains(p))break;if(!mt(r[p-1],i[d-1],o))break}m>0&&(j.addRange(new _(n.original.endLineNumberExclusive,n.original.endLineNumberExclusive+m)),B.addRange(new _(n.modified.endLineNumberExclusive,n.modified.endLineNumberExclusive+m)));if(h>0||m>0)v[t]=new I(new _(n.original.startLineNumber-h,n.original.endLineNumberExclusive+m),new _(n.modified.startLineNumber-h,n.modified.endLineNumberExclusive+m))};for(var x=0;x<v.length;x++)et(x);return v}function mt(e,t,n){function d(t){var n=0;for(var r=0;r<e.length;r++)Y(t.charCodeAt(r))||n++;return n}var r,i;if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;var o=new tt,u=o.compute(new ot([e],new C(1,1,1,e.length),!1),new ot([t],new C(1,1,1,t.length),!1),n),a=0,f=$.invert(u.diffs,e.length);try{for(var l=s(f),c=l.next();!c.done;c=l.next()){var h=c.value;h.seq1Range.forEach(function(t){Y(e.charCodeAt(t))||a++})}}catch(p){r={error:p}}finally{try{c&&!c.done&&(i=l.return)&&i.call(l)}finally{if(r)throw r.error}}var v=d(e.length>t.length?e:t),m=a/v>.6&&v>10;return m}function gt(e){if(e.length===0)return e;e.sort(g(function(e){return e.original.startLineNumber},y));var t=[e[0]];for(var n=1;n<e.length;n++){var r=t[t.length-1],i=e[n],s=i.original.startLineNumber-r.original.endLineNumberExclusive,o=i.modified.startLineNumber-r.modified.endLineNumberExclusive,u=s>=0&&o>=0;if(u&&s+o<=2){t[t.length-1]=r.join(i);continue}t.push(i)}return t}function yt(e,t){var n=new M(e);return t=t.filter(function(t){var r=n.findLastMonotonous(function(e){return e.original.startLineNumber<t.original.endLineNumberExclusive})||new I(new _(1,1),new _(1,1)),i=k(e,function(e){return e.modified.startLineNumber<t.modified.endLineNumberExclusive}),s=r!==i;return s}),t}function bt(e,t,n){var r=n;return r=wt(e,t,r),r=wt(e,t,r),r=Et(e,t,r),r}function wt(e,t,n){if(n.length===0)return n;var r=[];r.push(n[0]);for(var i=1;i<n.length;i++){var s=r[r.length-1],o=n[i];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){var u=o.seq1Range.start-s.seq1Range.endExclusive,a=void 0;for(a=1;a<=u;a++)if(e.getElement(o.seq1Range.start-a)!==e.getElement(o.seq1Range.endExclusive-a)||t.getElement(o.seq2Range.start-a)!==t.getElement(o.seq2Range.endExclusive-a))break;a--;if(a===u){r[r.length-1]=new $(new T(s.seq1Range.start,o.seq1Range.endExclusive-u),new T(s.seq2Range.start,o.seq2Range.endExclusive-u));continue}o=o.delta(-a)}r.push(o)}var f=[];for(var i=0;i<r.length-1;i++){var l=r[i+1],o=r[i];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){var u=l.seq1Range.start-o.seq1Range.endExclusive,a=void 0;for(a=0;a<u;a++)if(!e.isStronglyEqual(o.seq1Range.start+a,o.seq1Range.endExclusive+a)||!t.isStronglyEqual(o.seq2Range.start+a,o.seq2Range.endExclusive+a))break;if(a===u){r[i+1]=new $(new T(o.seq1Range.start+u,l.seq1Range.endExclusive),new T(o.seq2Range.start+u,l.seq2Range.endExclusive));continue}a>0&&(o=o.delta(a))}f.push(o)}return r.length>0&&f.push(r[r.length-1]),f}function Et(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(var r=0;r<n.length;r++){var i=r>0?n[r-1]:undefined,s=n[r],o=r+1<n.length?n[r+1]:undefined,u=new T(i?i.seq1Range.endExclusive+1:0,o?o.seq1Range.start-1:e.length),a=new T(i?i.seq2Range.endExclusive+1:0,o?o.seq2Range.start-1:t.length);s.seq1Range.isEmpty?n[r]=St(s,e,t,u,a):s.seq2Range.isEmpty&&(n[r]=St(s.swap(),t,e,a,u).swap())}return n}function St(e,t,n,r,i){var s=100,o=1;while(e.seq1Range.start-o>=r.start&&e.seq2Range.start-o>=i.start&&n.isStronglyEqual(e.seq2Range.start-o,e.seq2Range.endExclusive-o)&&o<s)o++;o--;var u=0;while(e.seq1Range.start+u<r.endExclusive&&e.seq2Range.endExclusive+u<i.endExclusive&&n.isStronglyEqual(e.seq2Range.start+u,e.seq2Range.endExclusive+u)&&u<s)u++;if(o===0&&u===0)return e;var a=0,f=-1;for(var l=-o;l<=u;l++){var c=e.seq2Range.start+l,h=e.seq2Range.endExclusive+l,p=e.seq1Range.start+l,d=t.getBoundaryScore(p)+n.getBoundaryScore(c)+n.getBoundaryScore(h);d>f&&(f=d,a=l)}return e.delta(a)}function xt(e,t,n){var r,i,o=[];try{for(var u=s(n),a=u.next();!a.done;a=u.next()){var f=a.value,l=o[o.length-1];if(!l){o.push(f);continue}f.seq1Range.start-l.seq1Range.endExclusive<=2||f.seq2Range.start-l.seq2Range.endExclusive<=2?o[o.length-1]=new $(l.seq1Range.join(f.seq1Range),l.seq2Range.join(f.seq2Range)):o.push(f)}}catch(c){r={error:c}}finally{try{a&&!a.done&&(i=u.return)&&i.call(u)}finally{if(r)throw r.error}}return o}function Tt(e,t,n,r,i){function a(n,a){if(n.offset1<u.offset1||n.offset2<u.offset2)return;var f=r(e,n.offset1),l=r(t,n.offset2);if(!f||!l)return;var c=new $(f,l),h=c.intersect(a),p=h.seq1Range.length,d=h.seq2Range.length;while(s.length>0){var v=s[0],m=v.seq1Range.intersects(c.seq1Range)||v.seq2Range.intersects(c.seq2Range);if(!m)break;var g=r(e,v.seq1Range.start),y=r(t,v.seq2Range.start),b=new $(g,y),w=b.intersect(v);p+=w.seq1Range.length,d+=w.seq2Range.length,c=c.join(b);if(!(c.seq1Range.endExclusive>=v.seq1Range.endExclusive))break;s.shift()}(i&&p+d<c.seq1Range.length+c.seq2Range.length||p+d<(c.seq1Range.length+c.seq2Range.length)*2/3)&&o.push(c),u=c.getEndExclusives()}i===void 0&&(i=!1);var s=$.invert(n,e.length),o=[],u=new J(0,0);while(s.length>0){var f=s.shift();if(f.seq1Range.isEmpty)continue;a(f.getStarts(),f),a(f.getEndExclusives().delta(-1),f)}var l=Nt(n,o);return l}function Nt(e,t){var n=[];while(e.length>0||t.length>0){var r=e[0],i=t[0],s=void 0;r&&(!i||r.seq1Range.start<i.seq1Range.start)?s=e.shift():s=t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=s.seq1Range.start?n[n.length-1]=n[n.length-1].join(s):n.push(s)}return n}function Ct(e,t,n){var r=n;if(r.length===0)return r;var i=0,s;do{s=!1;var o=[r[0]],u=function(t){var n=function(t,n){var r=new T(u.seq1Range.endExclusive,i.seq1Range.start),s=e.getText(r),o=s.replace(/\s/g,"");return o.length<=4&&(t.seq1Range.length+t.seq2Range.length>5||n.seq1Range.length+n.seq2Range.length>5)?!0:!1},i=r[t],u=o[o.length-1],a=n(u,i);a?(s=!0,o[o.length-1]=o[o.length-1].join(i)):o.push(i)};for(var a=1;a<r.length;a++)u(a);r=o}while(i++<10&&s);return r}function kt(e,t,n){var r=n;if(r.length===0)return r;var i=0,s;do{s=!1;var o=[r[0]],u=function(n){var i=function(n,r){function y(e){return Math.min(e,g)}var i=new T(a.seq1Range.endExclusive,u.seq1Range.start),s=e.countLinesIn(i);if(s>5||i.length>500)return!1;var o=e.getText(i).trim();if(o.length>20||o.split(/\r\n|\r|\n/).length>1)return!1;var f=e.countLinesIn(n.seq1Range),l=n.seq1Range.length,c=t.countLinesIn(n.seq2Range),h=n.seq2Range.length,p=e.countLinesIn(r.seq1Range),d=r.seq1Range.length,v=t.countLinesIn(r.seq2Range),m=r.seq2Range.length,g=130;return Math.pow(Math.pow(y(f*40+l),1.5)+Math.pow(y(c*40+h),1.5),1.5)+Math.pow(Math.pow(y(p*40+d),1.5)+Math.pow(y(v*40+m),1.5),1.5)>Math.pow(Math.pow(g,1.5),1.5)*1.3?!0:!1},u=r[n],a=o[o.length-1],f=i(a,u);f?(s=!0,o[o.length-1]=o[o.length-1].join(u)):o.push(u)};for(var a=1;a<r.length;a++)u(a);r=o}while(i++<10&&s);var f=[];return v(r,function(t,n,r){function s(e){return e.length>0&&e.trim().length<=3&&n.seq1Range.length+n.seq2Range.length>100}var i=n,o=e.extendToFullLines(n.seq1Range),u=e.getText(new T(o.start,n.seq1Range.start));s(u)&&(i=i.deltaStart(-u.length));var a=e.getText(new T(n.seq1Range.endExclusive,o.endExclusive));s(a)&&(i=i.deltaEnd(a.length));var l=$.fromOffsetPairs(t?t.getEndExclusives():J.zero,r?r.getStarts():J.max),c=i.intersect(l);f.length>0&&c.getStarts().equals(f[f.length-1].getEndExclusives())?f[f.length-1]=f[f.length-1].join(c):f.push(c)}),f}function At(e){var t=0;while(t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9))t++;return t}function Mt(e){return new I(new _(e.seq1Range.start+1,e.seq1Range.endExclusive+1),new _(e.seq2Range.start+1,e.seq2Range.endExclusive+1))}function _t(e,t,n){var r=new Ot,i=r.computeDiff(e,t,n);return i===null||i===void 0?void 0:i.changes.map(function(e){var t,n,r,i,s=e.innerChanges;return t=e.original.startLineNumber-1,n=e.original.endLineNumberExclusive-1,r=e.modified.startLineNumber-1,i=e.modified.endLineNumberExclusive-1,{origStart:t,origEnd:n,editStart:r,editEnd:i,charChanges:s===null||s===void 0?void 0:s.map(function(e){return{originalStartLineNumber:e.originalRange.startLineNumber-1,originalStartColumn:e.originalRange.startColumn-1,originalEndLineNumber:e.originalRange.endLineNumber-1,originalEndColumn:e.originalRange.endColumn-1,modifiedStartLineNumber:e.modifiedRange.startLineNumber-1,modifiedStartColumn:e.modifiedRange.startColumn-1,modifiedEndLineNumber:e.modifiedRange.endLineNumber-1,modifiedEndColumn:e.modifiedRange.endColumn-1}})}})}var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function r(){this.constructor=t}if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n),t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=this&&this.__generator||function(e,t){function u(e){return function(t){return a([e,t])}}function a(u){if(r)throw new TypeError("Generator is already executing.");while(o&&(o=0,u[0]&&(n=0)),n)try{if(r=1,i&&(s=u[0]&2?i["return"]:u[0]?i["throw"]||((s=i["return"])&&s.call(i),0):i.next)&&!(s=s.call(i,u[1])).done)return s;if(i=0,s)u=[u[0]&2,s.value];switch(u[0]){case 0:case 1:s=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,i=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(!(s=n.trys,s=s.length>0&&s[s.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!s||u[1]>s[0]&&u[1]<s[3])){n.label=u[1];break}if(u[0]===6&&n.label<s[1]){n.label=s[1],s=u;break}if(s&&n.label<s[2]){n.label=s[2],n.ops.push(u);break}s[2]&&n.ops.pop(),n.trys.pop();continue}u=t.call(e,n)}catch(a){u=[6,a],i=0}finally{r=s=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}var n={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},r,i,s,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=u(0),o["throw"]=u(1),o["return"]=u(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o},s=this&&this.__values||function(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},o,u,a,f,l,c,y=function(e,t){return e-t},w=function(e){function t(n){var r=e.call(this,n||"An unexpected bug occurred.")||this;return Object.setPrototypeOf(r,t.prototype),r}return r(t,e),t}(Error),T=function(){function e(e,t){this.start=e,this.endExclusive=t;if(e>t)throw new w("Invalid range: ".concat(this.toString()))}return e.fromTo=function(t,n){return new e(t,n)},e.addRange=function(t,n){var r=0;while(r<n.length&&n[r].endExclusive<t.start)r++;var i=r;while(i<n.length&&n[i].start<=t.endExclusive)i++;if(r===i)n.splice(r,0,t);else{var s=Math.min(t.start,n[r].start),o=Math.max(t.endExclusive,n[i-1].endExclusive);n.splice(r,i-r,new e(s,o))}},e.tryCreate=function(t,n){return t>n?undefined:new e(t,n)},e.ofLength=function(t){return new e(0,t)},e.ofStartAndLength=function(t,n){return new e(t,t+n)},e.emptyAt=function(t){return new e(t,t)},Object.defineProperty(e.prototype,"isEmpty",{get:function(){return this.start===this.endExclusive},enumerable:!1,configurable:!0}),e.prototype.delta=function(t){return new e(this.start+t,this.endExclusive+t)},e.prototype.deltaStart=function(t){return new e(this.start+t,this.endExclusive)},e.prototype.deltaEnd=function(t){return new e(this.start,this.endExclusive+t)},Object.defineProperty(e.prototype,"length",{get:function(){return this.endExclusive-this.start},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"[".concat(this.start,", ").concat(this.endExclusive,")")},e.prototype.equals=function(e){return this.start===e.start&&this.endExclusive===e.endExclusive},e.prototype.containsRange=function(e){return this.start<=e.start&&e.endExclusive<=this.endExclusive},e.prototype.contains=function(e){return this.start<=e&&e<this.endExclusive},e.prototype.join=function(t){return new e(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))},e.prototype.intersect=function(t){var n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return n<=r?new e(n,r):undefined},e.prototype.intersectionLength=function(e){var t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);return Math.max(0,n-t)},e.prototype.intersects=function(e){var t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);return t<n},e.prototype.intersectsOrTouches=function(e){var t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);return t<=n},e.prototype.isBefore=function(e){return this.endExclusive<=e.start},e.prototype.isAfter=function(e){return this.start>=e.endExclusive},e.prototype.slice=function(e){return e.slice(this.start,this.endExclusive)},e.prototype.substring=function(e){return e.substring(this.start,this.endExclusive)},e.prototype.clip=function(e){if(this.isEmpty)throw new w("Invalid clipping range: ".concat(this.toString()));return Math.max(this.start,Math.min(this.endExclusive-1,e))},e.prototype.clipCyclic=function(e){if(this.isEmpty)throw new w("Invalid clipping range: ".concat(this.toString()));return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e},e.prototype.map=function(e){var t=[];for(var n=this.start;n<this.endExclusive;n++)t.push(e(n));return t},e.prototype.forEach=function(e){for(var t=this.start;t<this.endExclusive;t++)e(t)},e}(),N=function(){function e(e,t){this.lineNumber=e,this.column=t}return e.prototype.equals=function(t){return e.equals(this,t)},e.equals=function(e,t){return!e&&!t?!0:!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column},e.prototype.isBefore=function(t){return e.isBefore(this,t)},e.isBefore=function(e,t){return e.lineNumber<t.lineNumber?!0:t.lineNumber<e.lineNumber?!1:e.column<t.column},e.prototype.isBeforeOrEqual=function(t){return e.isBeforeOrEqual(this,t)},e.isBeforeOrEqual=function(e,t){return e.lineNumber<t.lineNumber?!0:t.lineNumber<e.lineNumber?!1:e.column<=t.column},e}(),C=function(){function e(e,t,n,r){e>n||e===n&&t>r?(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=r)}return e.prototype.isEmpty=function(){return e.isEmpty(this)},e.isEmpty=function(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn},e.prototype.containsPosition=function(t){return e.containsPosition(this,t)},e.containsPosition=function(e,t){return t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber?!1:t.lineNumber===e.startLineNumber&&t.column<e.startColumn?!1:t.lineNumber===e.endLineNumber&&t.column>e.endColumn?!1:!0},e.prototype.containsRange=function(t){return e.containsRange(this,t)},e.containsRange=function(e,t){return t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber?!1:t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber?!1:t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn?!1:t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn?!1:!0},e.prototype.strictContainsRange=function(t){return e.strictContainsRange(this,t)},e.strictContainsRange=function(e,t){return t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber?!1:t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber?!1:t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn?!1:t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn?!1:!0},e.prototype.plusRange=function(t){return e.plusRange(this,t)},e.plusRange=function(t,n){var r,i,s,o;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(s=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(s=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(s=t.endLineNumber,o=t.endColumn),new e(r,i,s,o)},e.prototype.intersectRanges=function(t){return e.intersectRanges(this,t)},e.intersectRanges=function(t,n){var r=t.startLineNumber,i=t.startColumn,s=t.endLineNumber,o=t.endColumn,u=n.startLineNumber,a=n.startColumn,f=n.endLineNumber,l=n.endColumn;return r<u?(r=u,i=a):r===u&&(i=Math.max(i,a)),s>f?(s=f,o=l):s===f&&(o=Math.min(o,l)),r>s?null:r===s&&i>o?null:new e(r,i,s,o)},e.prototype.equalsRange=function(t){return e.equalsRange(this,t)},e.equalsRange=function(e,t){return!e&&!t?!0:!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn},e.prototype.getEndPosition=function(){return e.getEndPosition(this)},e.getEndPosition=function(e){return new N(e.endLineNumber,e.endColumn)},e.prototype.getStartPosition=function(){return e.getStartPosition(this)},e.getStartPosition=function(e){return new N(e.startLineNumber,e.startColumn)},e.prototype.collapseToStart=function(){return e.collapseToStart(this)},e.collapseToStart=function(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)},e.prototype.collapseToEnd=function(){return e.collapseToEnd(this)},e.collapseToEnd=function(t){return new e(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)},e.fromPositions=function(t,n){return n===void 0&&(n=t),new e(t.lineNumber,t.column,n.lineNumber,n.column)},e}(),M=function(){function e(e){this._array=e,this._findLastMonotonousLastIdx=0}return e.prototype.findLastMonotonous=function(e){var t,n;if(u.assertInvariants){if(this._prevFindLastPredicate)try{for(var r=s(this._array),i=r.next();!i.done;i=r.next()){var o=i.value;if(this._prevFindLastPredicate(o)&&!e(o))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}}catch(a){t={error:a}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}this._prevFindLastPredicate=e}var f=L(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=f+1,f===-1?undefined:this._array[f]},e}();u=M,function(){u.assertInvariants=!1}();var _=function(){function e(e,t){if(e>t)throw new w("startLineNumber ".concat(e," cannot be after endLineNumberExclusive ").concat(t));this.startLineNumber=e,this.endLineNumberExclusive=t}return e.fromRangeInclusive=function(t){return new e(t.startLineNumber,t.endLineNumber+1)},e.join=function(t){if(t.length===0)throw new w("lineRanges cannot be empty");var n=t[0].startLineNumber,r=t[0].endLineNumberExclusive;for(var i=1;i<t.length;i++)n=Math.min(n,t[i].startLineNumber),r=Math.max(r,t[i].endLineNumberExclusive);return new e(n,r)},e.ofLength=function(t,n){return new e(t,t+n)},Object.defineProperty(e.prototype,"isEmpty",{get:function(){return this.startLineNumber===this.endLineNumberExclusive},enumerable:!1,configurable:!0}),e.prototype.delta=function(t){return new e(this.startLineNumber+t,this.endLineNumberExclusive+t)},Object.defineProperty(e.prototype,"length",{get:function(){return this.endLineNumberExclusive-this.startLineNumber},enumerable:!1,configurable:!0}),e.prototype.join=function(t){return new e(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))},e.prototype.intersect=function(t){var n=Math.max(this.startLineNumber,t.startLineNumber),r=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);return n<=r?new e(n,r):undefined},e.prototype.overlapOrTouch=function(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive},e.prototype.toInclusiveRange=function(){return this.isEmpty?null:new C(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)},e.prototype.toOffsetRange=function(){return new T(this.startLineNumber-1,this.endLineNumberExclusive-1)},e}(),D=function(){function e(e){e===void 0&&(e=[]),this._normalizedRanges=e}return Object.defineProperty(e.prototype,"ranges",{get:function(){return this._normalizedRanges},enumerable:!1,configurable:!0}),e.prototype.addRange=function(e){if(e.length===0)return;var t=O(this._normalizedRanges,function(t){return t.endLineNumberExclusive>=e.startLineNumber}),n=L(this._normalizedRanges,function(t){return t.startLineNumber<=e.endLineNumberExclusive})+1;if(t===n)this._normalizedRanges.splice(t,0,e);else if(t===n-1){var r=this._normalizedRanges[t];this._normalizedRanges[t]=r.join(e)}else{var r=this._normalizedRanges[t].join(this._normalizedRanges[n-1]).join(e);this._normalizedRanges.splice(t,n-t,r)}},e.prototype.contains=function(e){var t=k(this._normalizedRanges,function(t){return t.startLineNumber<=e});return!!t&&t.endLineNumberExclusive>e},e.prototype.subtractFrom=function(t){var n=O(this._normalizedRanges,function(e){return e.endLineNumberExclusive>=t.startLineNumber}),r=L(this._normalizedRanges,function(e){return e.startLineNumber<=t.endLineNumberExclusive})+1;if(n===r)return new e([t]);var i=[],s=t.startLineNumber;for(var o=n;o<r;o++){var u=this._normalizedRanges[o];u.startLineNumber>s&&i.push(new _(s,u.startLineNumber)),s=u.endLineNumberExclusive}return s<t.endLineNumberExclusive&&i.push(new _(s,t.endLineNumberExclusive)),new e(i)},e.prototype.getIntersection=function(t){var n=[],r=0,i=0;while(r<this._normalizedRanges.length&&i<t._normalizedRanges.length){var s=this._normalizedRanges[r],o=t._normalizedRanges[i],u=s.intersect(o);u&&!u.isEmpty&&n.push(u),s.endLineNumberExclusive<o.endLineNumberExclusive?r++:i++}return new e(n)},e.prototype.getWithDelta=function(t){return new e(this._normalizedRanges.map(function(e){return e.delta(t)}))},e}(),P=function(){function e(e,t){this.lineCount=e,this.columnCount=t}return e.prototype.toLineRange=function(){return _.ofLength(1,this.lineCount)},e.prototype.addToPosition=function(e){return this.lineCount===0?new N(e.lineNumber,e.column+this.columnCount):new N(e.lineNumber+this.lineCount,this.columnCount+1)},e}();a=P,function(){a.zero=new a(0,0)}();var H=function(){function e(e,t){E(t>=1),this._getLineContent=e,this._lineCount=t}return e.prototype.getValueOfRange=function(e){if(e.startLineNumber===e.endLineNumber)return this._getLineContent(e.startLineNumber).substring(e.startColumn-1,e.endColumn-1);var t=this._getLineContent(e.startLineNumber).substring(e.startColumn-1);for(var n=e.startLineNumber+1;n<e.endLineNumber;n++)t+="\n"+this._getLineContent(n);return t+="\n"+this._getLineContent(e.endLineNumber).substring(0,e.endColumn-1),t},e.prototype.getLineLength=function(e){return this._getLineContent(e).length},Object.defineProperty(e.prototype,"length",{get:function(){var e=this._getLineContent(this._lineCount);return new P(this._lineCount-1,e.length)},enumerable:!1,configurable:!0}),e}(),B=function(e){function t(t){return e.call(this,function(e){return t[e-1]},t.length)||this}return r(t,e),t}(H),j=function(){function e(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}return e}(),F=function(){function e(e,t){this.lineRangeMapping=e,this.changes=t}return e}(),I=function(){function e(e,t){this.original=e,this.modified=t}return e.prototype.join=function(t){return new e(this.original.join(t.original),this.modified.join(t.modified))},Object.defineProperty(e.prototype,"changedLineCount",{get:function(){return Math.max(this.original.length,this.modified.length)},enumerable:!1,configurable:!0}),e.prototype.toRangeMapping=function(){var e=this.original.toInclusiveRange(),t=this.modified.toInclusiveRange();if(e&&t)return new z(e,t);if(this.original.startLineNumber===1||this.modified.startLineNumber===1){if(this.modified.startLineNumber!==1||this.original.startLineNumber!==1)throw new w("not a valid diff");return new z(new C(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new C(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1))}return new z(new C(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),new C(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER))},e.prototype.toRangeMapping2=function(e,t){if(R(this.original.endLineNumberExclusive,e)&&R(this.modified.endLineNumberExclusive,t))return new z(new C(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new C(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1));if(!this.original.isEmpty&&!this.modified.isEmpty)return new z(C.fromPositions(new N(this.original.startLineNumber,1),q(new N(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),e)),C.fromPositions(new N(this.modified.startLineNumber,1),q(new N(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),t)));if(this.original.startLineNumber>1&&this.modified.startLineNumber>1)return new z(C.fromPositions(q(new N(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER),e),q(new N(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),e)),C.fromPositions(q(new N(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER),t),q(new N(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),t)));throw new w},e}(),U=function(e){function t(t,n,r){var i=e.call(this,t,n)||this;return i.innerChanges=r,i}return r(t,e),t.fromRangeMappings=function(e){var n=_.join(e.map(function(e){return _.fromRangeInclusive(e.originalRange)})),r=_.join(e.map(function(e){return _.fromRangeInclusive(e.modifiedRange)}));return new t(n,r,e)},t.prototype.flip=function(){var e;return new t(this.modified,this.original,(e=this.innerChanges)===null||e===void 0?void 0:e.map(function(e){return e.flip()}))},t.prototype.withInnerChangesFromLineRanges=function(){return new t(this.original,this.modified,[this.toRangeMapping()])},t}(I),z=function(){function e(e,t){this.originalRange=e,this.modifiedRange=t}return e.join=function(e){if(e.length===0)throw new w("Cannot join an empty list of range mappings");var t=e[0];for(var n=1;n<e.length;n++)t=t.join(e[n]);return t},e.assertSorted=function(e){for(var t=1;t<e.length;t++){var n=e[t-1],r=e[t];if(!n.originalRange.getEndPosition().isBeforeOrEqual(r.originalRange.getStartPosition())||!n.modifiedRange.getEndPosition().isBeforeOrEqual(r.modifiedRange.getStartPosition()))throw new w("Range mappings must be sorted")}},e.prototype.flip=function(){return new e(this.modifiedRange,this.originalRange)},e.prototype.join=function(t){return new e(this.originalRange.plusRange(t.originalRange),this.modifiedRange.plusRange(t.modifiedRange))},e}(),V=function(){function e(e,t){this.diffs=e,this.hitTimeout=t}return e.trivial=function(t,n){return new e([new $(T.ofLength(t.length),T.ofLength(n.length))],!1)},e.trivialTimedOut=function(t,n){return new e([new $(T.ofLength(t.length),T.ofLength(n.length))],!0)},e}(),$=function(){function e(e,t){this.seq1Range=e,this.seq2Range=t}return e.invert=function(t,n){var r=[];return d(t,function(t,i){r.push(e.fromOffsetPairs(t?t.getEndExclusives():J.zero,i?i.getStarts():new J(n,(t?t.seq2Range.endExclusive-t.seq1Range.endExclusive:0)+n)))}),r},e.fromOffsetPairs=function(t,n){return new e(new T(t.offset1,n.offset1),new T(t.offset2,n.offset2))},e.assertSorted=function(e){var t,n,r=undefined;try{for(var i=s(e),o=i.next();!o.done;o=i.next()){var u=o.value;if(!(!r||r.seq1Range.endExclusive<=u.seq1Range.start&&r.seq2Range.endExclusive<=u.seq2Range.start))throw new w("Sequence diffs must be sorted");r=u}}catch(a){t={error:a}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e.prototype.swap=function(){return new e(this.seq2Range,this.seq1Range)},e.prototype.join=function(t){return new e(this.seq1Range.join(t.seq1Range),this.seq2Range.join(t.seq2Range))},e.prototype.delta=function(t){return t===0?this:new e(this.seq1Range.delta(t),this.seq2Range.delta(t))},e.prototype.deltaStart=function(t){return t===0?this:new e(this.seq1Range.deltaStart(t),this.seq2Range.deltaStart(t))},e.prototype.deltaEnd=function(t){return t===0?this:new e(this.seq1Range.deltaEnd(t),this.seq2Range.deltaEnd(t))},e.prototype.intersect=function(t){var n=this.seq1Range.intersect(t.seq1Range),r=this.seq2Range.intersect(t.seq2Range);return!n||!r?undefined:new e(n,r)},e.prototype.getStarts=function(){return new J(this.seq1Range.start,this.seq2Range.start)},e.prototype.getEndExclusives=function(){return new J(this.seq1Range.endExclusive,this.seq2Range.endExclusive)},e}(),J=function(){function e(e,t){this.offset1=e,this.offset2=t}return e.prototype.delta=function(e){return e===0?this:new f(this.offset1+e,this.offset2+e)},e.prototype.equals=function(e){return this.offset1===e.offset1&&this.offset2===e.offset2},e}();f=J,function(){f.zero=new f(0,0)}(),function(){f.max=new f(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER)}();var K=function(){function e(){}return e.prototype.isValid=function(){return!0},e}();l=K,function(){l.instance=new l}();var Q=function(){function e(e){this.timeout=e,this.startTime=Date.now(),this.valid=!0;if(e<=0)throw new w("timeout must be positive")}return e.prototype.isValid=function(){var e=Date.now()-this.startTime<this.timeout;return!e&&this.valid&&(this.valid=!1),this.valid},e.prototype.disable=function(){this.timeout=Number.MAX_SAFE_INTEGER,this.isValid=function(){return!0},this.valid=!0},e}(),G=function(){function e(e,t){this.width=e,this.height=t,this.array=[],this.array=new Array(e*t)}return e.prototype.get=function(e,t){return this.array[e+t*this.width]},e.prototype.set=function(e,t,n){this.array[e+t*this.width]=n},e}(),Z=function(){function e(e,t,n){this.range=e,this.lines=t,this.source=n,this.histogram=[];var r=0;for(var i=e.startLineNumber-1;i<e.endLineNumberExclusive-1;i++){var s=t[i];for(var o=0;o<s.length;o++){r++;var u=s[o],a=c.getKey(u);this.histogram[a]=(this.histogram[a]||0)+1}r++;var f=c.getKey("\n");this.histogram[f]=(this.histogram[f]||0)+1}this.totalCount=r}return e.getKey=function(e){var t=this.chrKeys.get(e);return t===undefined&&(t=this.chrKeys.size,this.chrKeys.set(e,t)),t},e.prototype.computeSimilarity=function(e){var t,n,r=0,i=Math.max(this.histogram.length,e.histogram.length);for(var s=0;s<i;s++)r+=Math.abs(((t=this.histogram[s])!==null&&t!==void 0?t:0)-((n=e.histogram[s])!==null&&n!==void 0?n:0));return 1-r/(this.totalCount+e.totalCount)},e}();c=Z,function(){c.chrKeys=new Map}();var et=function(){function e(){}return e.prototype.compute=function(e,t,n,r){function g(e,t){(e+1!==v||t+1!==m)&&d.push(new $(new T(e+1,v),new T(t+1,m))),v=e,m=t}n===void 0&&(n=K.instance);if(e.length===0||t.length===0)return V.trivial(e,t);var i=new G(e.length,t.length),s=new G(e.length,t.length),o=new G(e.length,t.length);for(var u=0;u<e.length;u++)for(var a=0;a<t.length;a++){if(!n.isValid())return V.trivialTimedOut(e,t);var f=u===0?0:i.get(u-1,a),l=a===0?0:i.get(u,a-1),c=void 0;e.getElement(u)===t.getElement(a)?(u===0||a===0?c=0:c=i.get(u-1,a-1),u>0&&a>0&&s.get(u-1,a-1)===3&&(c+=o.get(u-1,a-1)),c+=r?r(u,a):1):c=-1;var h=Math.max(f,l,c);if(h===c){var p=u>0&&a>0?o.get(u-1,a-1):0;o.set(u,a,p+1),s.set(u,a,3)}else h===f?(o.set(u,a,0),s.set(u,a,1)):h===l&&(o.set(u,a,0),s.set(u,a,2));i.set(u,a,h)}var d=[],v=e.length,m=t.length,y=e.length-1,b=t.length-1;while(y>=0&&b>=0)s.get(y,b)===3?(g(y,b),y--,b--):s.get(y,b)===1?y--:b--;return g(-1,-1),d.reverse(),new V(d,!1)},e}(),tt=function(){function e(){}return e.prototype.compute=function(e,t,n){function s(e,t){while(e<r.length&&t<i.length&&r.getElement(e)===i.getElement(t))e++,t++;return e}n===void 0&&(n=K.instance);if(e.length===0||t.length===0)return V.trivial(e,t);var r=e,i=t,o=0,u=new rt;u.set(0,s(0,0));var a=new it;a.set(0,u.get(0)===0?null:new nt(null,0,0,u.get(0)));var f=0;e:for(;;){o++;if(!n.isValid())return V.trivialTimedOut(r,i);var l=-Math.min(o,i.length+o%2),c=Math.min(o,r.length+o%2);for(f=l;f<=c;f+=2){var h=f===c?-1:u.get(f+1),p=f===l?-1:u.get(f-1)+1,d=Math.min(Math.max(h,p),r.length),v=d-f;if(d>r.length||v>i.length)continue;var m=s(d,v);u.set(f,m);var g=d===h?a.get(f+1):a.get(f-1);a.set(f,m!==d?new nt(g,d,v,m-d):g);if(u.get(f)===r.length&&u.get(f)-f===i.length)break e}}var y=a.get(f),b=[],w=r.length,E=i.length;for(;;){var S=y?y.x+y.length:0,x=y?y.y+y.length:0;(S!==w||x!==E)&&b.push(new $(new T(S,w),new T(x,E)));if(!y)break;w=y.x,E=y.y,y=y.prev}return b.reverse(),new V(b,!1)},e}(),nt=function(){function e(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}return e}(),rt=function(){function e(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}return e.prototype.get=function(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]},e.prototype.set=function(e,t){if(e<0){e=-e-1;if(e>=this.negativeArr.length){var n=this.negativeArr;this.negativeArr=new Int32Array(n.length*2),this.negativeArr.set(n)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){var n=this.positiveArr;this.positiveArr=new Int32Array(n.length*2),this.positiveArr.set(n)}this.positiveArr[e]=t}},e}(),it=function(){function e(){this.positiveArr=[],this.negativeArr=[]}return e.prototype.get=function(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]},e.prototype.set=function(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t},e}(),st=function(){function e(){this.map=new Map}return e.prototype.add=function(e,t){var n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)},e.prototype.forEach=function(e,t){var n=this.map.get(e);if(!n)return;n.forEach(t)},e.prototype.get=function(e){var t=this.map.get(e);return t?t:new Set},e}(),ot=function(){function e(e,t,n){this.lines=e,this.range=t,this.considerWhitespaceChanges=n,this.elements=[],this.firstElementOffsetByLineIdx=[],this.lineStartOffsets=[],this.trimmedWsLengthsByLineIdx=[],this.firstElementOffsetByLineIdx.push(0);for(var r=this.range.startLineNumber;r<=this.range.endLineNumber;r++){var i=e[r-1],s=0;r===this.range.startLineNumber&&this.range.startColumn>1&&(s=this.range.startColumn-1,i=i.substring(s)),this.lineStartOffsets.push(s);var o=0;if(!n){var u=i.trimStart();o=i.length-u.length,i=u.trimEnd()}this.trimmedWsLengthsByLineIdx.push(o);var a=r===this.range.endLineNumber?Math.min(this.range.endColumn-1-s-o,i.length):i.length;for(var f=0;f<a;f++)this.elements.push(i.charCodeAt(f));r<this.range.endLineNumber&&(this.elements.push("\n".charCodeAt(0)),this.firstElementOffsetByLineIdx.push(this.elements.length))}}return e.prototype.toString=function(){return'Slice: "'.concat(this.text,'"')},Object.defineProperty(e.prototype,"text",{get:function(){return this.getText(new T(0,this.length))},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){return this.elements.slice(e.start,e.endExclusive).map(function(e){return String.fromCharCode(e)}).join("")},e.prototype.getElement=function(e){return this.elements[e]},Object.defineProperty(e.prototype,"length",{get:function(){return this.elements.length},enumerable:!1,configurable:!0}),e.prototype.getBoundaryScore=function(e){var t=ct(e>0?this.elements[e-1]:-1),n=ct(e<this.elements.length?this.elements[e]:-1);if(t===7&&n===8)return 0;if(t===8)return 150;var r=0;return t!==n&&(r+=10,t===0&&n===1&&(r+=1)),r+=lt(t),r+=lt(n),r},e.prototype.translateOffset=function(e,t){t===void 0&&(t="right");var n=L(this.firstElementOffsetByLineIdx,function(t){return t<=e}),r=e-this.firstElementOffsetByLineIdx[n];return new N(this.range.startLineNumber+n,1+this.lineStartOffsets[n]+r+(r===0&&t==="left"?0:this.trimmedWsLengthsByLineIdx[n]))},e.prototype.translateRange=function(e){var t=this.translateOffset(e.start,"right"),n=this.translateOffset(e.endExclusive,"left");return n.isBefore(t)?C.fromPositions(n,n):C.fromPositions(t,n)},e.prototype.findWordContaining=function(e){if(e<0||e>=this.elements.length)return undefined;if(!ut(this.elements[e]))return undefined;var t=e;while(t>0&&ut(this.elements[t-1]))t--;var n=e;while(n<this.elements.length&&ut(this.elements[n]))n++;return new T(t,n)},e.prototype.findSubWordContaining=function(e){if(e<0||e>=this.elements.length)return undefined;if(!ut(this.elements[e]))return undefined;var t=e;while(t>0&&ut(this.elements[t-1])&&!at(this.elements[t]))t--;var n=e;while(n<this.elements.length&&ut(this.elements[n])&&!at(this.elements[n]))n++;return new T(t,n)},e.prototype.countLinesIn=function(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber},e.prototype.isStronglyEqual=function(e,t){return this.elements[e]===this.elements[t]},e.prototype.extendToFullLines=function(e){var t,n,r=(t=k(this.firstElementOffsetByLineIdx,function(t){return t<=e.start}))!==null&&t!==void 0?t:0,i=(n=A(this.firstElementOffsetByLineIdx,function(t){return e.endExclusive<=t}))!==null&&n!==void 0?n:this.elements.length;return new T(r,i)},e}(),ft=(o={},o[0]=0,o[1]=0,o[2]=0,o[3]=10,o[4]=2,o[5]=30,o[6]=3,o[7]=10,o[8]=10,o),Lt=function(){function e(e,t){this.trimmedHash=e,this.lines=t}return e.prototype.getElement=function(e){return this.trimmedHash[e]},Object.defineProperty(e.prototype,"length",{get:function(){return this.trimmedHash.length},enumerable:!1,configurable:!0}),e.prototype.getBoundaryScore=function(e){var t=e===0?0:At(this.lines[e-1]),n=e===this.lines.length?0:At(this.lines[e]);return 1e3-(t+n)},e.prototype.getText=function(e){return this.lines.slice(e.start,e.endExclusive).join("\n")},e.prototype.isStronglyEqual=function(e,t){return this.lines[e]===this.lines[t]},e}(),Ot=function(){function e(){this.dynamicProgrammingDiffing=new et,this.myersDiffingAlgorithm=new tt}return e.prototype.computeDiff=function(e,t,n){function l(e){var t=f.get(e);return t===undefined&&(t=f.size,f.set(e,t)),t}var r,i,o=this;if(e.length<=1&&h(e,t,function(e,t){return e===t}))return new j([],[],!1);if(e.length===1&&e[0].length===0||t.length===1&&t[0].length===0)return new j([new U(new _(1,e.length+1),new _(1,t.length+1),[new z(new C(1,1,e.length,e[e.length-1].length+1),new C(1,1,t.length,t[t.length-1].length+1))])],[],!1);var u=n.maxComputationTimeMs===0?K.instance:new Q(n.maxComputationTimeMs),a=!n.ignoreTrimWhitespace,f=new Map,c=e.map(function(e){return l(e.trim())}),p=t.map(function(e){return l(e.trim())}),d=new Lt(c,e),v=new Lt(p,t),m=function(){return d.length+v.length<1700?o.dynamicProgrammingDiffing.compute(d,v,u,function(n,r){return e[n]===t[r]?t[r].length===0?.1:1+Math.log(1+t[r].length):.99}):o.myersDiffingAlgorithm.compute(d,v,u)}(),g=m.diffs,y=m.hitTimeout;g=bt(d,v,g),g=Ct(d,v,g);var b=[],w=function(r){var i,f;if(!a)return;for(var l=0;l<r;l++){var c=E+l,h=x+l;if(e[c]!==t[h]){var p=o.refineDiff(e,t,new $(new T(c,c+1),new T(h,h+1)),u,a,n);try{for(var d=(i=void 0,s(p.mappings)),v=d.next();!v.done;v=d.next()){var m=v.value;b.push(m)}}catch(g){i={error:g}}finally{try{v&&!v.done&&(f=d.return)&&f.call(d)}finally{if(i)throw i.error}}p.hitTimeout&&(y=!0)}}},E=0,x=0,N=function(r){var i,o;S(function(){return r.seq1Range.start-E===r.seq2Range.start-x});var f=r.seq1Range.start-E;w(f),E=r.seq1Range.endExclusive,x=r.seq2Range.endExclusive;var l=k.refineDiff(e,t,r,u,a,n);l.hitTimeout&&(y=!0);try{for(var c=(i=void 0,s(l.mappings)),h=c.next();!h.done;h=c.next()){var p=h.value;b.push(p)}}catch(d){i={error:d}}finally{try{h&&!h.done&&(o=c.return)&&o.call(c)}finally{if(i)throw i.error}}},k=this;try{for(var L=s(g),A=L.next();!A.done;A=L.next()){var O=A.value;N(O)}}catch(M){r={error:M}}finally{try{A&&!A.done&&(i=L.return)&&i.call(L)}finally{if(r)throw r.error}}w(e.length-E);var D=W(b,new B(e),new B(t)),P=[];return n.computeMoves&&(P=this.computeMoves(D,e,t,c,p,u,a,n)),S(function(){function u(e,t){if(e.lineNumber<1||e.lineNumber>t.length)return!1;var n=t[e.lineNumber-1];return e.column<1||e.column>n.length+1?!1:!0}function a(e,t){return e.startLineNumber<1||e.startLineNumber>t.length+1?!1:e.endLineNumberExclusive<1||e.endLineNumberExclusive>t.length+1?!1:!0}var n,r,i,o;try{for(var f=s(D),l=f.next();!l.done;l=f.next()){var c=l.value;if(!c.innerChanges)return!1;try{for(var h=(i=void 0,s(c.innerChanges)),p=h.next();!p.done;p=h.next()){var d=p.value,v=u(d.modifiedRange.getStartPosition(),t)&&u(d.modifiedRange.getEndPosition(),t)&&u(d.originalRange.getStartPosition(),e)&&u(d.originalRange.getEndPosition(),e);if(!v)return!1}}catch(m){i={error:m}}finally{try{p&&!p.done&&(o=h.return)&&o.call(h)}finally{if(i)throw i.error}}if(!a(c.modified,t)||!a(c.original,e))return!1}}catch(g){n={error:g}}finally{try{l&&!l.done&&(r=f.return)&&r.call(f)}finally{if(n)throw n.error}}return!0}),new j(D,P,y)},e.prototype.computeMoves=function(e,t,n,r,i,s,o,u){var a=this,f=ht(e,t,n,r,i,s),l=f.map(function(e){var r=a.refineDiff(t,n,new $(e.original.toOffsetRange(),e.modified.toOffsetRange()),s,o,u),i=W(r.mappings,new B(t),new B(n),!0);return new F(e,i)});return l},e.prototype.refineDiff=function(e,t,n,r,i,s){var o=Mt(n),u=o.toRangeMapping2(e,t),a=new ot(e,u.originalRange,i),f=new ot(t,u.modifiedRange,i),l=a.length+f.length<500?this.dynamicProgrammingDiffing.compute(a,f,r):this.myersDiffingAlgorithm.compute(a,f,r),c=l.diffs;c=bt(a,f,c),c=Tt(a,f,c,function(e,t){return e.findWordContaining(t)}),s.extendToSubwords&&(c=Tt(a,f,c,function(e,t){return e.findSubWordContaining(t)},!0)),c=xt(a,f,c),c=kt(a,f,c);var h=c.map(function(e){return new z(a.translateRange(e.seq1Range),f.translateRange(e.seq2Range))});return{mappings:h,hitTimeout:l.hitTimeout}},e}();t.computeDiff=_t;var Dt=e("../../../range").Range,Pt=e("../base_diff_view").DiffChunk,Ht=function(){function e(){}return e.prototype.compute=function(e,t,n){n||(n={}),n.maxComputationTimeMs||(n.maxComputationTimeMs=500);var r=_t(e,t,n)||[];return r.map(function(e){return new Pt(new Dt(e.origStart,0,e.origEnd,0),new Dt(e.editStart,0,e.editEnd,0),e.charChanges)})},e}();t.DiffProvider=Ht}),ace.define("ace/ext/diff",["require","exports","module","ace/ext/diff/inline_diff_view","ace/ext/diff/split_diff_view","ace/ext/diff/providers/default"],function(e,t,n){function o(e,t){e=e||{},e.diffProvider=e.diffProvider||new s;var n;return e.inline?n=new r(e):n=new i(e),t&&n.setOptions(t),n}var r=e("./diff/inline_diff_view").InlineDiffView,i=e("./diff/split_diff_view").SplitDiffView,s=e("./diff/providers/default").DiffProvider;t.InlineDiffView=r,t.SplitDiffView=i,t.DiffProvider=s,t.createDiffView=o});                (function() {
                    ace.require(["ace/ext/diff"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            