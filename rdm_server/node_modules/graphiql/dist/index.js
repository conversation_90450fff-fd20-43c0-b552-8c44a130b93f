"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphiQLProvider = exports.GraphiQLInterface = exports.GraphiQL = void 0;
var GraphiQL_1 = require("./components/GraphiQL");
Object.defineProperty(exports, "GraphiQL", { enumerable: true, get: function () { return GraphiQL_1.GraphiQL; } });
Object.defineProperty(exports, "GraphiQLInterface", { enumerable: true, get: function () { return GraphiQL_1.GraphiQLInterface; } });
var react_1 = require("@graphiql/react");
Object.defineProperty(exports, "GraphiQLProvider", { enumerable: true, get: function () { return react_1.GraphiQLProvider; } });
exports.default = GraphiQL_1.GraphiQL;
//# sourceMappingURL=index.js.map