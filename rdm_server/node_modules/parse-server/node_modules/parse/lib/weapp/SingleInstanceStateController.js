"use strict";

var _WeakMap = require("@babel/runtime-corejs3/core-js-stable/weak-map");
var _Object$defineProperty = require("@babel/runtime-corejs3/core-js-stable/object/define-property");
var _Object$getOwnPropertyDescriptor = require("@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor");
_Object$defineProperty(exports, "__esModule", {
  value: true
});
exports.clearAllState = clearAllState;
exports.commitServerChanges = commitServerChanges;
exports.duplicateState = duplicateState;
exports.enqueueTask = enqueueTask;
exports.estimateAttribute = estimateAttribute;
exports.estimateAttributes = estimateAttributes;
exports.getObjectCache = getObjectCache;
exports.getPendingOps = getPendingOps;
exports.getServerData = getServerData;
exports.getState = getState;
exports.initializeState = initializeState;
exports.mergeFirstPendingState = mergeFirstPendingState;
exports.popPendingState = popPendingState;
exports.pushPendingState = pushPendingState;
exports.removeState = removeState;
exports.setPendingOp = setPendingOp;
exports.setServerData = setServerData;
var ObjectStateMutations = _interopRequireWildcard(require("./ObjectStateMutations"));
function _getRequireWildcardCache(e) {
  if ("function" != typeof _WeakMap) return null;
  var r = new _WeakMap(),
    t = new _WeakMap();
  return (_getRequireWildcardCache = function (e) {
    return e ? t : r;
  })(e);
}
function _interopRequireWildcard(e, r) {
  if (!r && e && e.__esModule) return e;
  if (null === e || "object" != typeof e && "function" != typeof e) return {
    default: e
  };
  var t = _getRequireWildcardCache(r);
  if (t && t.has(e)) return t.get(e);
  var n = {
    __proto__: null
  };
  for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) {
    var i = _Object$defineProperty && _Object$getOwnPropertyDescriptor ? _Object$getOwnPropertyDescriptor(e, u) : null;
    i && (i.get || i.set) ? _Object$defineProperty(n, u, i) : n[u] = e[u];
  }
  return n.default = e, t && t.set(e, n), n;
}
let objectState = {};
function getState(obj) {
  const classData = objectState[obj.className];
  if (classData) {
    return classData[obj.id] || null;
  }
  return null;
}
function initializeState(obj, initial) {
  let state = getState(obj);
  if (state) {
    return state;
  }
  if (!objectState[obj.className]) {
    objectState[obj.className] = {};
  }
  if (!initial) {
    initial = ObjectStateMutations.defaultState();
  }
  state = objectState[obj.className][obj.id] = initial;
  return state;
}
function removeState(obj) {
  const state = getState(obj);
  if (state === null) {
    return null;
  }
  delete objectState[obj.className][obj.id];
  return state;
}
function getServerData(obj) {
  const state = getState(obj);
  if (state) {
    return state.serverData;
  }
  return {};
}
function setServerData(obj, attributes) {
  const serverData = initializeState(obj).serverData;
  ObjectStateMutations.setServerData(serverData, attributes);
}
function getPendingOps(obj) {
  const state = getState(obj);
  if (state) {
    return state.pendingOps;
  }
  return [{}];
}
function setPendingOp(obj, attr, op) {
  const pendingOps = initializeState(obj).pendingOps;
  ObjectStateMutations.setPendingOp(pendingOps, attr, op);
}
function pushPendingState(obj) {
  const pendingOps = initializeState(obj).pendingOps;
  ObjectStateMutations.pushPendingState(pendingOps);
}
function popPendingState(obj) {
  const pendingOps = initializeState(obj).pendingOps;
  return ObjectStateMutations.popPendingState(pendingOps);
}
function mergeFirstPendingState(obj) {
  const pendingOps = getPendingOps(obj);
  ObjectStateMutations.mergeFirstPendingState(pendingOps);
}
function getObjectCache(obj) {
  const state = getState(obj);
  if (state) {
    return state.objectCache;
  }
  return {};
}
function estimateAttribute(obj, attr) {
  const serverData = getServerData(obj);
  const pendingOps = getPendingOps(obj);
  return ObjectStateMutations.estimateAttribute(serverData, pendingOps, obj, attr);
}
function estimateAttributes(obj) {
  const serverData = getServerData(obj);
  const pendingOps = getPendingOps(obj);
  return ObjectStateMutations.estimateAttributes(serverData, pendingOps, obj);
}
function commitServerChanges(obj, changes) {
  const state = initializeState(obj);
  ObjectStateMutations.commitServerChanges(state.serverData, state.objectCache, changes);
}
function enqueueTask(obj, task) {
  const state = initializeState(obj);
  return state.tasks.enqueue(task);
}
function clearAllState() {
  objectState = {};
}
function duplicateState(source, dest) {
  dest.id = source.id;
}