"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
/* global localStorage */

const StorageController = {
  async: 0,
  getItem(path) {
    return localStorage.getItem(path);
  },
  setItem(path, value) {
    try {
      localStorage.setItem(path, value);
    } catch (e) {
      // Quota exceeded, possibly due to Safari Private Browsing mode
      console.log(e.message);
    }
  },
  removeItem(path) {
    localStorage.removeItem(path);
  },
  getAllKeys() {
    const keys = [];
    for (let i = 0; i < localStorage.length; i += 1) {
      keys.push(localStorage.key(i));
    }
    return keys;
  },
  clear() {
    localStorage.clear();
  }
};
var _default = exports.default = StorageController;