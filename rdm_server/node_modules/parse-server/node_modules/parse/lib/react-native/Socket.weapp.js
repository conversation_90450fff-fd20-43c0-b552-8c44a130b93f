var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var SocketWeapp = function () {
  function SocketWeapp(serverURL) {
    var _this = this;
    (0, _classCallCheck2.default)(this, SocketWeapp);
    this.onopen = function () {};
    this.onmessage = function () {};
    this.onclose = function () {};
    this.onerror = function () {};
    wx.onSocketOpen(function () {
      _this.onopen();
    });
    wx.onSocketMessage(function (msg) {
      _this.onmessage(msg);
    });
    wx.onSocketClose(function (event) {
      _this.onclose(event);
    });
    wx.onSocketError(function (error) {
      _this.onerror(error);
    });
    wx.connectSocket({
      url: serverURL
    });
  }
  return (0, _createClass2.default)(SocketWeapp, [{
    key: "send",
    value: function (data) {
      wx.sendSocketMessage({
        data: data
      });
    }
  }, {
    key: "close",
    value: function () {
      wx.closeSocket();
    }
  }]);
}();
var _default = exports.default = SocketWeapp;