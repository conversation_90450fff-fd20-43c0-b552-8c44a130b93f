var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _CoreManager = _interopRequireDefault(require("./CoreManager"));
var _ParseACL = _interopRequireDefault(require("./ParseACL"));
var _ParseError = _interopRequireDefault(require("./ParseError"));
var _ParseObject2 = _interopRequireDefault(require("./ParseObject"));
function _callSuper(t, o, e) {
  return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e));
}
function _isNativeReflectConstruct() {
  try {
    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));
  } catch (t) {}
  return (_isNativeReflectConstruct = function () {
    return !!t;
  })();
}
function _superPropGet(t, o, e, r) {
  var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e);
  return 2 & r && "function" == typeof p ? function (t) {
    return p.apply(e, t);
  } : p;
}
var ParseRole = function (_ParseObject) {
  function ParseRole(name, acl) {
    var _this;
    (0, _classCallCheck2.default)(this, ParseRole);
    _this = _callSuper(this, ParseRole, ['_Role']);
    if (typeof name === 'string' && acl instanceof _ParseACL.default) {
      _this.setName(name);
      _this.setACL(acl);
    }
    return _this;
  }
  (0, _inherits2.default)(ParseRole, _ParseObject);
  return (0, _createClass2.default)(ParseRole, [{
    key: "getName",
    value: function () {
      var name = this.get('name');
      if (name == null || typeof name === 'string') {
        return name;
      }
      return '';
    }
  }, {
    key: "setName",
    value: function (name, options) {
      this._validateName(name);
      return this.set('name', name, options);
    }
  }, {
    key: "getUsers",
    value: function () {
      return this.relation('users');
    }
  }, {
    key: "getRoles",
    value: function () {
      return this.relation('roles');
    }
  }, {
    key: "_validateName",
    value: function (newName) {
      if (typeof newName !== 'string') {
        throw new _ParseError.default(_ParseError.default.OTHER_CAUSE, "A role's name must be a String.");
      }
      if (!/^[0-9a-zA-Z\-_ ]+$/.test(newName)) {
        throw new _ParseError.default(_ParseError.default.OTHER_CAUSE, "A role's name can be only contain alphanumeric characters, _, " + '-, and spaces.');
      }
    }
  }, {
    key: "validate",
    value: function (attrs, options) {
      var isInvalid = _superPropGet(ParseRole, "validate", this, 1)(attrs, options);
      if (isInvalid) {
        return isInvalid;
      }
      if ('name' in attrs && attrs.name !== this.getName()) {
        var newName = attrs.name;
        if (this.id && this.id !== attrs.objectId) {
          return new _ParseError.default(_ParseError.default.OTHER_CAUSE, "A role's name can only be set before it has been saved.");
        }
        try {
          this._validateName(newName);
        } catch (e) {
          return e;
        }
      }
      return false;
    }
  }]);
}(_ParseObject2.default);
_CoreManager.default.setParseRole(ParseRole);
_ParseObject2.default.registerSubclass('_Role', ParseRole);
var _default = exports.default = ParseRole;