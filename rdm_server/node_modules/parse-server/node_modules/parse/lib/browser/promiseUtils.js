"use strict";

var _Object$defineProperty = require("@babel/runtime-corejs3/core-js-stable/object/define-property");
var _interopRequireDefault = require("@babel/runtime-corejs3/helpers/interopRequireDefault");
_Object$defineProperty(exports, "__esModule", {
  value: true
});
exports.continueWhile = continueWhile;
exports.resolvingPromise = resolvingPromise;
exports.when = when;
var _promise = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/promise"));
var _isArray = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/array/is-array"));
// Create Deferred Promise
function resolvingPromise() {
  let res;
  let rej;
  const promise = new _promise.default((resolve, reject) => {
    res = resolve;
    rej = reject;
  });
  const defer = promise;
  defer.resolve = res;
  defer.reject = rej;
  return defer;
}
function when(promises) {
  let objects;
  const arrayArgument = (0, _isArray.default)(promises);
  if (arrayArgument) {
    objects = promises;
  } else {
    objects = arguments;
  }
  let total = objects.length;
  let hadError = false;
  const results = [];
  const returnValue = arrayArgument ? [results] : results;
  const errors = [];
  results.length = objects.length;
  errors.length = objects.length;
  if (total === 0) {
    return _promise.default.resolve(returnValue);
  }
  const promise = resolvingPromise();
  const resolveOne = function () {
    total--;
    if (total <= 0) {
      if (hadError) {
        promise.reject(errors);
      } else {
        promise.resolve(returnValue);
      }
    }
  };
  const chain = function (object, index) {
    if (object && typeof object.then === 'function') {
      object.then(function (result) {
        results[index] = result;
        resolveOne();
      }, function (error) {
        errors[index] = error;
        hadError = true;
        resolveOne();
      });
    } else {
      results[index] = object;
      resolveOne();
    }
  };
  for (let i = 0; i < objects.length; i++) {
    chain(objects[i], i);
  }
  return promise;
}
function continueWhile(test, emitter) {
  if (test()) {
    return emitter().then(() => {
      return continueWhile(test, emitter);
    });
  }
  return _promise.default.resolve();
}