"use strict";

var _Config = _interopRequireDefault(require("../../Config"));
var _Deprecator = _interopRequireDefault(require("../../Deprecator/Deprecator"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
/*
 * PhantAuth was designed to simplify testing for applications using OpenID Connect
 * authentication by making use of random generated users.
 *
 * To learn more, please go to: https://www.phantauth.net
 */

const {
  Parse
} = require('parse/node');
const httpsRequest = require('./httpsRequest');
// Returns a promise that fulfills if this user id is valid.
async function validateAuthData(authData) {
  const config = _Config.default.get(Parse.applicationId);
  _Deprecator.default.logRuntimeDeprecation({
    usage: 'phantauth adapter'
  });
  const phantauthConfig = config.auth.phantauth;
  if (!phantauthConfig?.enableInsecureAuth) {
    throw new Parse.Error(Parse.Error.INTERNAL_SERVER_ERROR, 'PhantAuth only works with enableInsecureAuth: true');
  }
  const data = await request('auth/userinfo', authData.access_token);
  if (data?.sub !== authData.id) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'PhantAuth auth is invalid for this user.');
  }
}

// Returns a promise that fulfills if this app id is valid.
function validateAppId() {
  return Promise.resolve();
}

// A promisey wrapper for api requests
function request(path, access_token) {
  return httpsRequest.get({
    host: 'phantauth.net',
    path: '/' + path,
    headers: {
      Authorization: 'bearer ' + access_token,
      'User-Agent': 'parse-server'
    }
  });
}
module.exports = {
  validateAppId: validateAppId,
  validateAuthData: validateAuthData
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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