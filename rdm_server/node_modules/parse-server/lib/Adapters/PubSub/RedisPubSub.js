"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RedisPubSub = void 0;
var _redis = require("redis");
var _logger = require("../../logger");
function createPublisher({
  redisURL,
  redisOptions = {}
}) {
  redisOptions.no_ready_check = true;
  const client = (0, _redis.createClient)({
    url: redisURL,
    ...redisOptions
  });
  client.on('error', err => {
    _logger.logger.error('RedisPubSub Publisher client error', {
      error: err
    });
  });
  client.on('connect', () => {});
  client.on('reconnecting', () => {});
  client.on('ready', () => {});
  return client;
}
function createSubscriber({
  redisURL,
  redisOptions = {}
}) {
  redisOptions.no_ready_check = true;
  const client = (0, _redis.createClient)({
    url: redisURL,
    ...redisOptions
  });
  client.on('error', err => {
    _logger.logger.error('RedisPubSub Subscriber client error', {
      error: err
    });
  });
  client.on('connect', () => {});
  client.on('reconnecting', () => {});
  client.on('ready', () => {});
  return client;
}
const RedisPubSub = exports.RedisPubSub = {
  createPublisher,
  createSubscriber
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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