"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.PushWorker = void 0;
var _deepcopy = _interopRequireDefault(require("deepcopy"));
var _AdaptableController = _interopRequireDefault(require("../Controllers/AdaptableController"));
var _Auth = require("../Auth");
var _Config = _interopRequireDefault(require("../Config"));
var _PushAdapter = require("../Adapters/Push/PushAdapter");
var _rest = _interopRequireDefault(require("../rest"));
var _StatusHandler = require("../StatusHandler");
var utils = _interopRequireWildcard(require("./utils"));
var _ParseMessageQueue = require("../ParseMessageQueue");
var _PushQueue = require("./PushQueue");
var _logger = _interopRequireDefault(require("../logger"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// -disable-next
function groupByBadge(installations) {
  return installations.reduce((map, installation) => {
    const badge = installation.badge + '';
    map[badge] = map[badge] || [];
    map[badge].push(installation);
    return map;
  }, {});
}
class PushWorker {
  constructor(pushAdapter, subscriberConfig = {}) {
    _AdaptableController.default.validateAdapter(pushAdapter, this, _PushAdapter.PushAdapter);
    this.adapter = pushAdapter;
    this.channel = subscriberConfig.channel || _PushQueue.PushQueue.defaultPushChannel();
    this.subscriber = _ParseMessageQueue.ParseMessageQueue.createSubscriber(subscriberConfig);
    if (this.subscriber) {
      const subscriber = this.subscriber;
      subscriber.subscribe(this.channel);
      subscriber.on('message', (channel, messageStr) => {
        const workItem = JSON.parse(messageStr);
        this.run(workItem);
      });
    }
  }
  run({
    body,
    query,
    pushStatus,
    applicationId,
    UTCOffset
  }) {
    const config = _Config.default.get(applicationId);
    const auth = (0, _Auth.master)(config);
    const where = utils.applyDeviceTokenExists(query.where);
    delete query.where;
    pushStatus = (0, _StatusHandler.pushStatusHandler)(config, pushStatus.objectId);
    return _rest.default.find(config, auth, '_Installation', where, query).then(({
      results
    }) => {
      if (results.length == 0) {
        return;
      }
      return this.sendToAdapter(body, results, pushStatus, config, UTCOffset);
    });
  }
  sendToAdapter(body, installations, pushStatus, config, UTCOffset) {
    // Check if we have locales in the push body
    const locales = utils.getLocalesFromPush(body);
    if (locales.length > 0) {
      // Get all tranformed bodies for each locale
      const bodiesPerLocales = utils.bodiesPerLocales(body, locales);

      // Group installations on the specified locales (en, fr, default etc...)
      const grouppedInstallations = utils.groupByLocaleIdentifier(installations, locales);
      const promises = Object.keys(grouppedInstallations).map(locale => {
        const installations = grouppedInstallations[locale];
        const body = bodiesPerLocales[locale];
        return this.sendToAdapter(body, installations, pushStatus, config, UTCOffset);
      });
      return Promise.all(promises);
    }
    if (!utils.isPushIncrementing(body)) {
      _logger.default.verbose(`Sending push to ${installations.length}`);
      return this.adapter.send(body, installations, pushStatus.objectId).then(results => {
        return pushStatus.trackSent(results, UTCOffset).then(() => results);
      });
    }

    // Collect the badges to reduce the # of calls
    const badgeInstallationsMap = groupByBadge(installations);

    // Map the on the badges count and return the send result
    const promises = Object.keys(badgeInstallationsMap).map(badge => {
      const payload = (0, _deepcopy.default)(body);
      payload.data.badge = parseInt(badge);
      const installations = badgeInstallationsMap[badge];
      return this.sendToAdapter(payload, installations, pushStatus, config, UTCOffset);
    });
    return Promise.all(promises);
  }
}
exports.PushWorker = PushWorker;
var _default = exports.default = PushWorker;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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