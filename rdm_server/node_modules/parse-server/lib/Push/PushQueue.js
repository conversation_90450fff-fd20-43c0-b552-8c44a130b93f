"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PushQueue = void 0;
var _ParseMessageQueue = require("../ParseMessageQueue");
var _rest = _interopRequireDefault(require("../rest"));
var _utils = require("./utils");
var _node = _interopRequireDefault(require("parse/node"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const PUSH_CHANNEL = 'parse-server-push';
const DEFAULT_BATCH_SIZE = 100;
class PushQueue {
  // config object of the publisher, right now it only contains the redisURL,
  // but we may extend it later.
  constructor(config = {}) {
    this.channel = config.channel || PushQueue.defaultPushChannel();
    this.batchSize = config.batchSize || DEFAULT_BATCH_SIZE;
    this.parsePublisher = _ParseMessageQueue.ParseMessageQueue.createPublisher(config);
  }
  static defaultPushChannel() {
    return `${_node.default.applicationId}-${PUSH_CHANNEL}`;
  }
  enqueue(body, where, config, auth, pushStatus) {
    const limit = this.batchSize;
    where = (0, _utils.applyDeviceTokenExists)(where);

    // Order by objectId so no impact on the DB
    const order = 'objectId';
    return Promise.resolve().then(() => {
      return _rest.default.find(config, auth, '_Installation', where, {
        limit: 0,
        count: true
      });
    }).then(({
      results,
      count
    }) => {
      if (!results || count == 0) {
        return pushStatus.complete();
      }
      pushStatus.setRunning(Math.ceil(count / limit));
      let skip = 0;
      while (skip < count) {
        const query = {
          where,
          limit,
          skip,
          order
        };
        const pushWorkItem = {
          body,
          query,
          pushStatus: {
            objectId: pushStatus.objectId
          },
          applicationId: config.applicationId
        };
        this.parsePublisher.publish(this.channel, JSON.stringify(pushWorkItem));
        skip += limit;
      }
    });
  }
}
exports.PushQueue = PushQueue;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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