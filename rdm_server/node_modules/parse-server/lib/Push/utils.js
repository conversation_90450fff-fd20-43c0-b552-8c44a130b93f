"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.applyDeviceTokenExists = applyDeviceTokenExists;
exports.bodiesPerLocales = bodiesPerLocales;
exports.getLocalesFromPush = getLocalesFromPush;
exports.groupByLocaleIdentifier = groupByLocaleIdentifier;
exports.isPushIncrementing = isPushIncrementing;
exports.stripLocalesFromBody = stripLocalesFromBody;
exports.transformPushBodyForLocale = transformPushBodyForLocale;
exports.validatePushType = validatePushType;
var _node = _interopRequireDefault(require("parse/node"));
var _deepcopy = _interopRequireDefault(require("deepcopy"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function isPushIncrementing(body) {
  if (!body.data || !body.data.badge) {
    return false;
  }
  const badge = body.data.badge;
  if (typeof badge == 'string' && badge.toLowerCase() == 'increment') {
    return true;
  }
  return typeof badge == 'object' && typeof badge.__op == 'string' && badge.__op.toLowerCase() == 'increment' && Number(badge.amount);
}
const localizableKeys = ['alert', 'title'];
function getLocalesFromPush(body) {
  const data = body.data;
  if (!data) {
    return [];
  }
  return [...new Set(Object.keys(data).reduce((memo, key) => {
    localizableKeys.forEach(localizableKey => {
      if (key.indexOf(`${localizableKey}-`) == 0) {
        memo.push(key.slice(localizableKey.length + 1));
      }
    });
    return memo;
  }, []))];
}
function transformPushBodyForLocale(body, locale) {
  const data = body.data;
  if (!data) {
    return body;
  }
  body = (0, _deepcopy.default)(body);
  localizableKeys.forEach(key => {
    const localeValue = body.data[`${key}-${locale}`];
    if (localeValue) {
      body.data[key] = localeValue;
    }
  });
  return stripLocalesFromBody(body);
}
function stripLocalesFromBody(body) {
  if (!body.data) {
    return body;
  }
  Object.keys(body.data).forEach(key => {
    localizableKeys.forEach(localizableKey => {
      if (key.indexOf(`${localizableKey}-`) == 0) {
        delete body.data[key];
      }
    });
  });
  return body;
}
function bodiesPerLocales(body, locales = []) {
  // Get all tranformed bodies for each locale
  const result = locales.reduce((memo, locale) => {
    memo[locale] = transformPushBodyForLocale(body, locale);
    return memo;
  }, {});
  // Set the default locale, with the stripped body
  result.default = stripLocalesFromBody(body);
  return result;
}
function groupByLocaleIdentifier(installations, locales = []) {
  return installations.reduce((map, installation) => {
    let added = false;
    locales.forEach(locale => {
      if (added) {
        return;
      }
      if (installation.localeIdentifier && installation.localeIdentifier.indexOf(locale) === 0) {
        added = true;
        map[locale] = map[locale] || [];
        map[locale].push(installation);
      }
    });
    if (!added) {
      map.default.push(installation);
    }
    return map;
  }, {
    default: []
  });
}

/**
 * Check whether the deviceType parameter in qury condition is valid or not.
 * @param {Object} where A query condition
 * @param {Array} validPushTypes An array of valid push types(string)
 */
function validatePushType(where = {}, validPushTypes = []) {
  var deviceTypeField = where.deviceType || {};
  var deviceTypes = [];
  if (typeof deviceTypeField === 'string') {
    deviceTypes.push(deviceTypeField);
  } else if (Array.isArray(deviceTypeField['$in'])) {
    deviceTypes.concat(deviceTypeField['$in']);
  }
  for (var i = 0; i < deviceTypes.length; i++) {
    var deviceType = deviceTypes[i];
    if (validPushTypes.indexOf(deviceType) < 0) {
      throw new _node.default.Error(_node.default.Error.PUSH_MISCONFIGURED, deviceType + ' is not supported push type.');
    }
  }
}
function applyDeviceTokenExists(where) {
  where = (0, _deepcopy.default)(where);
  if (!Object.prototype.hasOwnProperty.call(where, 'deviceToken')) {
    where['deviceToken'] = {
      $exists: true
    };
  }
  return where;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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