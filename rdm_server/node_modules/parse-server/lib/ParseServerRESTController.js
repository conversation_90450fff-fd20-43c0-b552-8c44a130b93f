"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ParseServerRESTController = ParseServerRESTController;
exports.default = void 0;
var _RESTController = _interopRequireDefault(require("parse/lib/node/RESTController"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const Config = require('./Config');
const Auth = require('./Auth');
const Parse = require('parse/node');
function getSessionToken(options) {
  if (options && typeof options.sessionToken === 'string') {
    return Promise.resolve(options.sessionToken);
  }
  return Promise.resolve(null);
}
function getAuth(options = {}, config) {
  const installationId = options.installationId || 'cloud';
  if (options.useMasterKey) {
    return Promise.resolve(new Auth.Auth({
      config,
      isMaster: true,
      installationId
    }));
  }
  return getSessionToken(options).then(sessionToken => {
    if (sessionToken) {
      options.sessionToken = sessionToken;
      return Auth.getAuthForSessionToken({
        config,
        sessionToken: sessionToken,
        installationId
      });
    } else {
      return Promise.resolve(new Auth.Auth({
        config,
        installationId
      }));
    }
  });
}
function ParseServerRESTController(applicationId, router) {
  function handleRequest(method, path, data = {}, options = {}, config) {
    // Store the arguments, for later use if internal fails
    const args = arguments;
    if (!config) {
      config = Config.get(applicationId);
    }
    const serverURL = new URL(config.serverURL);
    if (path.indexOf(serverURL.pathname) === 0) {
      path = path.slice(serverURL.pathname.length, path.length);
    }
    if (path[0] !== '/') {
      path = '/' + path;
    }
    if (path === '/batch') {
      const batch = transactionRetries => {
        let initialPromise = Promise.resolve();
        if (data.transaction === true) {
          initialPromise = config.database.createTransactionalSession();
        }
        return initialPromise.then(() => {
          const promises = data.requests.map(request => {
            return handleRequest(request.method, request.path, request.body, options, config).then(response => {
              if (options.returnStatus) {
                const status = response._status;
                const headers = response._headers;
                delete response._status;
                delete response._headers;
                return {
                  success: response,
                  _status: status,
                  _headers: headers
                };
              }
              return {
                success: response
              };
            }, error => {
              return {
                error: {
                  code: error.code,
                  error: error.message
                }
              };
            });
          });
          return Promise.all(promises).then(result => {
            if (data.transaction === true) {
              if (result.find(resultItem => typeof resultItem.error === 'object')) {
                return config.database.abortTransactionalSession().then(() => {
                  return Promise.reject(result);
                });
              } else {
                return config.database.commitTransactionalSession().then(() => {
                  return result;
                });
              }
            } else {
              return result;
            }
          }).catch(error => {
            if (error && error.find(errorItem => typeof errorItem.error === 'object' && errorItem.error.code === 251) && transactionRetries > 0) {
              return batch(transactionRetries - 1);
            }
            throw error;
          });
        });
      };
      return batch(5);
    }
    let query;
    if (method === 'GET') {
      query = data;
    }
    return new Promise((resolve, reject) => {
      getAuth(options, config).then(auth => {
        const request = {
          body: data,
          config,
          auth,
          info: {
            applicationId: applicationId,
            sessionToken: options.sessionToken,
            installationId: options.installationId,
            context: options.context || {}
          },
          query
        };
        return Promise.resolve().then(() => {
          return router.tryRouteRequest(method, path, request);
        }).then(resp => {
          const {
            response,
            status,
            headers = {}
          } = resp;
          if (options.returnStatus) {
            resolve({
              ...response,
              _status: status,
              _headers: headers
            });
          } else {
            resolve(response);
          }
        }, err => {
          if (err instanceof Parse.Error && err.code == Parse.Error.INVALID_JSON && err.message == `cannot route ${method} ${path}`) {
            _RESTController.default.request.apply(null, args).then(resolve, reject);
          } else {
            reject(err);
          }
        });
      }, reject);
    });
  }
  return {
    request: handleRequest,
    ajax: _RESTController.default.ajax,
    handleError: _RESTController.default.handleError
  };
}
var _default = exports.default = ParseServerRESTController;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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