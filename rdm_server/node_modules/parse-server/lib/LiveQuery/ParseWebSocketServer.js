"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ParseWebSocketServer = exports.ParseWebSocket = void 0;
var _AdapterLoader = require("../Adapters/AdapterLoader");
var _WSAdapter = require("../Adapters/WebSocketServer/WSAdapter");
var _logger = _interopRequireDefault(require("../logger"));
var _events = _interopRequireDefault(require("events"));
var _util = require("util");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
class ParseWebSocketServer {
  constructor(server, onConnect, config) {
    config.server = server;
    const wss = (0, _AdapterLoader.loadAdapter)(config.wssAdapter, _WSAdapter.WSAdapter, config);
    wss.onListen = () => {
      _logger.default.info('Parse LiveQuery Server started running');
    };
    wss.onConnection = ws => {
      ws.waitingForPong = false;
      ws.on('pong', () => {
        ws.waitingForPong = false;
      });
      ws.on('error', error => {
        _logger.default.error(error.message);
        _logger.default.error((0, _util.inspect)(ws, false));
      });
      onConnect(new ParseWebSocket(ws));
      // Send ping to client periodically
      const pingIntervalId = setInterval(() => {
        if (!ws.waitingForPong) {
          ws.ping();
          ws.waitingForPong = true;
        } else {
          clearInterval(pingIntervalId);
          ws.terminate();
        }
      }, config.websocketTimeout || 10 * 1000);
    };
    wss.onError = error => {
      _logger.default.error(error);
    };
    wss.start();
    this.server = wss;
  }
  close() {
    if (this.server && this.server.close) {
      this.server.close();
    }
  }
}
exports.ParseWebSocketServer = ParseWebSocketServer;
class ParseWebSocket extends _events.default.EventEmitter {
  constructor(ws) {
    super();
    ws.onmessage = request => this.emit('message', request && request.data ? request.data : request);
    ws.onclose = () => this.emit('disconnect');
    this.ws = ws;
  }
  send(message) {
    this.ws.send(message);
  }
}
exports.ParseWebSocket = ParseWebSocket;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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