"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const general = {
  title: 'General request schema',
  type: 'object',
  properties: {
    op: {
      type: 'string',
      enum: ['connect', 'subscribe', 'unsubscribe', 'update']
    }
  },
  required: ['op']
};
const connect = {
  title: 'Connect operation schema',
  type: 'object',
  properties: {
    op: 'connect',
    applicationId: {
      type: 'string'
    },
    javascriptKey: {
      type: 'string'
    },
    masterKey: {
      type: 'string'
    },
    clientKey: {
      type: 'string'
    },
    windowsKey: {
      type: 'string'
    },
    restAPIKey: {
      type: 'string'
    },
    sessionToken: {
      type: 'string'
    },
    installationId: {
      type: 'string'
    }
  },
  required: ['op', 'applicationId'],
  additionalProperties: false
};
const subscribe = {
  title: 'Subscribe operation schema',
  type: 'object',
  properties: {
    op: 'subscribe',
    requestId: {
      type: 'number'
    },
    query: {
      title: 'Query field schema',
      type: 'object',
      properties: {
        className: {
          type: 'string'
        },
        where: {
          type: 'object'
        },
        keys: {
          type: 'array',
          items: {
            type: 'string'
          },
          minItems: 1,
          uniqueItems: true
        },
        watch: {
          type: 'array',
          items: {
            type: 'string'
          },
          minItems: 1,
          uniqueItems: true
        }
      },
      required: ['where', 'className'],
      additionalProperties: false
    },
    sessionToken: {
      type: 'string'
    }
  },
  required: ['op', 'requestId', 'query'],
  additionalProperties: false
};
const update = {
  title: 'Update operation schema',
  type: 'object',
  properties: {
    op: 'update',
    requestId: {
      type: 'number'
    },
    query: {
      title: 'Query field schema',
      type: 'object',
      properties: {
        className: {
          type: 'string'
        },
        where: {
          type: 'object'
        },
        keys: {
          type: 'array',
          items: {
            type: 'string'
          },
          minItems: 1,
          uniqueItems: true
        },
        watch: {
          type: 'array',
          items: {
            type: 'string'
          },
          minItems: 1,
          uniqueItems: true
        }
      },
      required: ['where', 'className'],
      additionalProperties: false
    },
    sessionToken: {
      type: 'string'
    }
  },
  required: ['op', 'requestId', 'query'],
  additionalProperties: false
};
const unsubscribe = {
  title: 'Unsubscribe operation schema',
  type: 'object',
  properties: {
    op: 'unsubscribe',
    requestId: {
      type: 'number'
    }
  },
  required: ['op', 'requestId'],
  additionalProperties: false
};
const RequestSchema = {
  general: general,
  connect: connect,
  subscribe: subscribe,
  update: update,
  unsubscribe: unsubscribe
};
var _default = exports.default = RequestSchema;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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