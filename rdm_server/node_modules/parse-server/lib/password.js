"use strict";

// Tools for encrypting and decrypting passwords.
// Basically promise-friendly wrappers for bcrypt.
var bcrypt = require('bcryptjs');
try {
  const _bcrypt = require('@node-rs/bcrypt');
  bcrypt = {
    hash: _bcrypt.hash,
    compare: _bcrypt.verify
  };
} catch (e) {
  /* */
}

// Returns a promise for a hashed password string.
function hash(password) {
  return bcrypt.hash(password, 10);
}

// Returns a promise for whether this password compares to equal this
// hashed password.
function compare(password, hashedPassword) {
  // Cannot bcrypt compare when one is undefined
  if (!password || !hashedPassword) {
    return Promise.resolve(false);
  }
  return bcrypt.compare(password, hashedPassword);
}
module.exports = {
  hash: hash,
  compare: compare
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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